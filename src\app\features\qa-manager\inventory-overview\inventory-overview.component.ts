import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';

// QA Manager Components
import { AddCashModalService } from '../components/add-cash-modal';
import type { AddCashResult, AddCashDialogData } from '../components/add-cash-modal';
import { RemoveCashModalService } from '../components/remove-cash-modal';
import type { RemoveCashResult, RemoveCashDialogData } from '../components/remove-cash-modal';
import { AddCoinModalService } from '../components/add-coin-modal/add-coin-modal.service';
import type { AddCoinResult, AddCoinDialogData } from '../components/add-coin-modal/add-coin-modal.component';
import { RemoveCoinModalService } from '../components/remove-coin-modal/remove-coin-modal.service';
import type { RemoveCoinResult, RemoveCoinDialogData } from '../components/remove-coin-modal/remove-coin-modal.component';
import { AuditReportModalService } from '../components/audit-report-modal';
import { NoteSeriesManagementModalService } from '../components/note-series-management-modal';

// Inventory Models
import {
  CashInventory,
  CoinSeries,
  CoinDenomination,
  CoinInventory,
  NoteSeries,
  NoteDenomination,
  NOTE_SERIES_LABELS,
  DENOMINATION_LABELS,
  COIN_SERIES_LABELS,
  COIN_DENOMINATION_LABELS,
  COIN_BATCH_CONFIG,
  COIN_BATCH_VALUES
} from '../../../shared/models/inventory.model';
import { InventoryService } from '../../../shared/services/inventory.service';

@Component({
  selector: 'app-inventory-overview',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatTabsModule,
    MatSnackBarModule,
    MatTooltipModule
  ],
  templateUrl: './inventory-overview.component.html',
  styleUrls: ['./inventory-overview.component.scss']
})
export class InventoryOverviewComponent implements OnInit {
  // Inventory data loaded from service
  inventorySummary: any = {
    totalValue: 0,
    totalNotes: 0,
    lowStockAlerts: []
  };

  // Inventory data loaded from service
  inventoryData: any[] = [];

  // Make labels available to template
  NOTE_SERIES_LABELS = NOTE_SERIES_LABELS;

  // Series data loaded from service
  seriesData: any[] = [];

  // Note series count for summary card
  noteSeriesCount: number = 0;

  // Default series data (will be replaced by actual data)
  private defaultSeriesData: any[] = [
    {
      id: 'mandela',
      name: 'Mandela Series',
      totalBatches: 122,
      totalSingles: 77,
      totalValue: 854040.00,
      denominations: [
        {
          value: 10,
          batches: 20,
          singles: 87,
          totalValue: 20870.00,
          stockLevel: 100
        },
        {
          value: 20,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 50,
          batches: 30,
          singles: 79,
          totalValue: 153950.00,
          stockLevel: 100
        },
        {
          value: 100,
          batches: 32,
          singles: 61,
          totalValue: 65220.00,
          stockLevel: 100
        },
        {
          value: 200,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        }
      ]
    },
    {
      id: 'big5',
      name: 'Big 5 Series',
      totalBatches: 95,
      totalSingles: 43,
      totalValue: 720500.00,
      denominations: [
        {
          value: 10,
          batches: 5,
          singles: 12,
          totalValue: 512.00,
          stockLevel: 25
        },
        {
          value: 20,
          batches: 25,
          singles: 43,
          totalValue: 50860.00,
          stockLevel: 85
        },
        {
          value: 50,
          batches: 8,
          singles: 15,
          totalValue: 4015.00,
          stockLevel: 35
        },
        {
          value: 100,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 200,
          batches: 18,
          singles: 22,
          totalValue: 362200.00,
          stockLevel: 45
        }
      ]
    },
    {
      id: 'commemorative',
      name: 'Commemorative Series',
      totalBatches: 45,
      totalSingles: 23,
      totalValue: 125000.00,
      denominations: [
        {
          value: 10,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 20,
          batches: 3,
          singles: 8,
          totalValue: 608.00,
          stockLevel: 15
        },
        {
          value: 50,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 100,
          batches: 12,
          singles: 8,
          totalValue: 120800.00,
          stockLevel: 75
        },
        {
          value: 200,
          batches: 2,
          singles: 5,
          totalValue: 4005.00,
          stockLevel: 8
        }
      ]
    },
    {
      id: 'v6',
      name: 'V6 Series',
      totalBatches: 67,
      totalSingles: 34,
      totalValue: 890000.00,
      denominations: [
        {
          value: 10,
          batches: 15,
          singles: 45,
          totalValue: 1545.00,
          stockLevel: 65
        },
        {
          value: 20,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 50,
          batches: 12,
          singles: 33,
          totalValue: 6033.00,
          stockLevel: 55
        },
        {
          value: 100,
          batches: 22,
          singles: 22,
          totalValue: 222200.00,
          stockLevel: 90
        },
        {
          value: 200,
          batches: 15,
          singles: 12,
          totalValue: 302400.00,
          stockLevel: 60
        }
      ]
    }
  ];

  DENOMINATION_LABELS: { [key: number]: string } = {
    10: 'R10',
    20: 'R20',
    50: 'R50',
    100: 'R100',
    200: 'R200'
  };

  // Coin inventory data loaded from service
  coinInventory: CoinInventory[] = [];

  // Grouped coin inventory by denomination (aggregated across all series)
  groupedCoinInventory: CoinInventory[] = [];

  // Coin configuration
  coinDenominations = Object.values(CoinDenomination);
  coinLabels = COIN_DENOMINATION_LABELS;
  coinBatchConfig = COIN_BATCH_CONFIG;
  coinBatchValues = COIN_BATCH_VALUES;

  // Mock user service for demo
  userService = {
    hasManagerPrivileges: () => true
  };

  constructor(
    private addCashModalService: AddCashModalService,
    private removeCashModalService: RemoveCashModalService,
    private addCoinModalService: AddCoinModalService,
    private removeCoinModalService: RemoveCoinModalService,
    private snackBar: MatSnackBar,
    private inventoryService: InventoryService,
    private auditReportModalService: AuditReportModalService,
    private noteSeriesManagementModalService: NoteSeriesManagementModalService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    // Load inventory data from service
    this.loadInventoryData();

    // Subscribe to inventory changes
    this.inventoryService.cashInventory$.subscribe(cashInventory => {
      console.log('Cash inventory updated via subscription:', cashInventory);
      this.updateInventoryData(cashInventory);
      this.cdr.detectChanges();
    });

    this.inventoryService.coinInventory$.subscribe(coinInventory => {
      console.log('Coin inventory updated via subscription:', coinInventory);
      this.coinInventory = coinInventory;
      this.groupedCoinInventory = this.groupCoinsByDenomination(coinInventory);
      this.cdr.detectChanges();
    });

    // Subscribe to custom note series changes
    this.inventoryService.customNoteSeries$.subscribe(() => {
      console.log('Custom note series updated via subscription');
      this.updateNoteSeriesCount();
      // Refresh series data when custom series change
      const cashInventory = this.inventoryService.getCashInventory();
      this.seriesData = this.transformToSeriesData(cashInventory);
      this.cdr.detectChanges();
    });
  }

  private loadInventoryData(): void {
    console.log('Loading inventory data from service...');
    // Load current inventory data
    const cashInventory = this.inventoryService.getCashInventory();
    const coinInventory = this.inventoryService.getCoinInventory();
    const summary = this.inventoryService.getInventorySummary();

    console.log('Cash inventory loaded:', cashInventory);
    console.log('Current seriesData before update:', this.seriesData);

    this.updateInventoryData(cashInventory);
    this.coinInventory = coinInventory;
    this.groupedCoinInventory = this.groupCoinsByDenomination(coinInventory);
    this.inventorySummary = summary;

    console.log('SeriesData after update:', this.seriesData);
  }

  private updateInventoryData(cashInventory: CashInventory[]): void {
    // Transform cash inventory data for the template
    this.inventoryData = this.transformCashInventoryData(cashInventory);
    this.seriesData = this.transformToSeriesData(cashInventory);

    // Update note series count
    this.updateNoteSeriesCount();
  }

  /**
   * Groups coin inventory by denomination, aggregating quantities and values across all series
   */
  private groupCoinsByDenomination(coinInventory: CoinInventory[]): CoinInventory[] {
    const denominationMap = new Map<CoinDenomination, CoinInventory>();

    coinInventory.forEach(coin => {
      const existing = denominationMap.get(coin.denomination);

      if (existing) {
        // Aggregate quantities and values
        existing.quantity += coin.quantity;
        existing.batches += coin.batches;
        existing.value += coin.value;
        // Update timestamp to the most recent
        if (coin.lastUpdated > existing.lastUpdated) {
          existing.lastUpdated = coin.lastUpdated;
          existing.updatedBy = coin.updatedBy;
        }
      } else {
        // Create new aggregated entry
        denominationMap.set(coin.denomination, {
          id: `coin-aggregated-${coin.denomination}`,
          series: coin.series, // Use the first series found (for display purposes)
          denomination: coin.denomination,
          quantity: coin.quantity,
          batches: coin.batches,
          value: coin.value,
          lastUpdated: coin.lastUpdated,
          updatedBy: coin.updatedBy
        });
      }
    });

    // Convert map to array and sort by denomination value (descending)
    return Array.from(denominationMap.values()).sort((a, b) => b.denomination - a.denomination);
  }

  private transformCashInventoryData(cashInventory: CashInventory[]): any[] {
    // Group by series and create the structure expected by the template
    const seriesGroups: { [key: string]: any } = {};
    const allSeries = this.inventoryService.getAllNoteSeries();

    cashInventory.forEach(item => {
      // For custom series, extract the series ID from the item ID
      let seriesKey: string = item.noteSeries;
      let seriesName = NOTE_SERIES_LABELS[item.noteSeries];

      // Check if this is a custom series
      if (!seriesName) {
        // First check if noteSeries itself is a custom series ID
        const customSeries = allSeries.find(s => s.id === item.noteSeries);
        if (customSeries) {
          seriesName = customSeries.name;
        } else if (item.id.startsWith('cash-custom_')) {
          // Fallback: extract from item ID format: cash-{seriesId}-{denomination}
          const parts = item.id.split('-');
          if (parts.length >= 3) {
            seriesKey = parts[1]; // Extract custom series ID
            const customSeriesById = allSeries.find(s => s.id === seriesKey);
            seriesName = customSeriesById ? customSeriesById.name : seriesKey;
          }
        }
      }

      if (!seriesGroups[seriesKey]) {
        seriesGroups[seriesKey] = {
          id: seriesKey,
          name: seriesName || seriesKey,
          denominations: {}
        };
      }

      seriesGroups[seriesKey].denominations[item.denomination] = {
        value: item.denomination,
        quantity: item.quantity,
        totalValue: item.value
      };
    });

    return Object.values(seriesGroups);
  }

  private transformToSeriesData(cashInventory: CashInventory[]): any[] {
    // Group by series and create detailed series data
    const seriesGroups: { [key: string]: any } = {};
    const allSeries = this.inventoryService.getAllNoteSeries();

    cashInventory.forEach(item => {
      // For custom series, extract the series ID from the item ID
      let seriesKey: string = item.noteSeries;
      let seriesName = NOTE_SERIES_LABELS[item.noteSeries];

      // Check if this is a custom series
      if (!seriesName) {
        // First check if noteSeries itself is a custom series ID
        const customSeries = allSeries.find(s => s.id === item.noteSeries);
        if (customSeries) {
          seriesName = customSeries.name;
        } else if (item.id.startsWith('cash-custom_')) {
          // Fallback: extract from item ID format: cash-{seriesId}-{denomination}
          const parts = item.id.split('-');
          if (parts.length >= 3) {
            seriesKey = parts[1]; // Extract custom series ID
            const customSeriesById = allSeries.find(s => s.id === seriesKey);
            seriesName = customSeriesById ? customSeriesById.name : seriesKey;
          }
        }
      }

      if (!seriesGroups[seriesKey]) {
        seriesGroups[seriesKey] = {
          id: seriesKey,
          name: seriesName || seriesKey,
          totalBatches: 0,
          totalSingles: 0,
          totalValue: 0,
          denominations: []
        };
      }

      const batches = Math.floor(item.quantity / 100);
      const singles = item.quantity % 100;
      const stockLevel = this.calculateStockLevel(item.quantity, item.denomination);

      seriesGroups[seriesKey].totalBatches += batches;
      seriesGroups[seriesKey].totalSingles += singles;
      seriesGroups[seriesKey].totalValue += item.value;

      seriesGroups[seriesKey].denominations.push({
        value: item.denomination,
        batches,
        singles,
        totalValue: item.value,
        stockLevel
      });
    });

    return Object.values(seriesGroups);
  }

  /**
   * Update the note series count for the summary card
   */
  private updateNoteSeriesCount(): void {
    const allSeries = this.inventoryService.getAllNoteSeries();
    this.noteSeriesCount = allSeries.length;
  }

  private calculateStockLevel(quantity: number, denomination: NoteDenomination): number {
    // Calculate stock level as percentage based on some maximum expected quantity
    const maxQuantities: { [key in NoteDenomination]: number } = {
      [NoteDenomination.R10]: 2000,
      [NoteDenomination.R20]: 1500,
      [NoteDenomination.R50]: 1200,
      [NoteDenomination.R100]: 800,
      [NoteDenomination.R200]: 400
    };

    const maxQuantity = maxQuantities[denomination];
    return Math.min(Math.round((quantity / maxQuantity) * 100), 100);
  }

  // Modal event handlers - these methods are no longer needed as we use individual modal services
  // onModalClose and onBackdropClick methods removed as they're not used with the new modal services

  // Tab change handler
  onTabChange(event: any): void {
    console.log('Tab changed to:', event.index);
  }

  // Track by function for denomination cards
  trackByDenomination(index: number, denomination: any): any {
    return denomination.value;
  }

  // Get status class based on stock level
  getStatusClass(stockLevel: number): string {
    if (stockLevel === 0) return 'status-out-of-stock';
    if (stockLevel >= 80) return 'status-good';
    if (stockLevel >= 50) return 'status-medium';
    return 'status-low';
  }

  // Get status text based on stock level
  getStatusText(stockLevel: number): string {
    if (stockLevel === 0) return 'Out of Stock';
    if (stockLevel >= 80) return 'In Stock';
    if (stockLevel >= 50) return 'Medium';
    return 'Low Stock';
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  }

  formatNumber(num: number): string {
    return new Intl.NumberFormat('en-ZA').format(num);
  }

  getDenominationLabel(denomination: number): string {
    return this.DENOMINATION_LABELS[denomination] || `R${denomination}`;
  }

  formatQuantityDisplay(quantity: number): string {
    const batches = Math.floor(quantity / 100);
    const singles = quantity % 100;

    if (batches === 0) {
      return `${singles} single${singles !== 1 ? 's' : ''}`;
    } else if (singles === 0) {
      return `${batches} batch${batches !== 1 ? 'es' : ''}`;
    } else {
      return `${batches} batch${batches !== 1 ? 'es' : ''} + ${singles} single${singles !== 1 ? 's' : ''}`;
    }
  }



  getStockStatus(item: any): { status: string; class: string } {
    if (item.quantity < 100) {
      return { status: 'Low Stock', class: 'low-stock' };
    } else if (item.quantity > 500) {
      return { status: 'High Stock', class: 'high-stock' };
    }
    return { status: 'Normal', class: 'normal-stock' };
  }

  getStockStatusIcon(item: any): string {
    const status = this.getStockStatus(item);
    if (status.class === 'low-stock') return 'warning';
    if (status.class === 'high-stock') return 'trending_up';
    return 'check_circle';
  }

  getStockPercentage(item: any): number {
    const maxStock = 1000; // Assume max stock is 1000
    return Math.min((item.quantity / maxStock) * 100, 100);
  }

  getStockProgressColor(item: any): string {
    const percentage = this.getStockPercentage(item);
    if (percentage < 20) return 'warn';
    if (percentage > 80) return 'accent';
    return 'primary';
  }

  getSeriesStyleClass(series: string): string {
    return `series-${series.toLowerCase()}`;
  }

  getDenominationImage(denomination: number): string {
    return `assets/images/Money/R${denomination}.jpg`;
  }

  onImageError(event: any): void {
    event.target.style.display = 'none';
  }

  onAddCash(seriesId?: string, denominationValue?: number): void {
    // If called without parameters (from FAB), open modal with no pre-selection
    if (!seriesId || !denominationValue) {
      const modalData: AddCashDialogData = {};

      this.addCashModalService.openAddCashModal(modalData)
        .subscribe((result: AddCashResult | undefined) => {
          if (result) {
            // For FAB calls, we don't have specific series/denomination context
            this.handleAddCashResult(result, '', 0);
          }
        });
      return;
    }

    // Get current quantity for this denomination
    const currentQuantity = this.getCurrentQuantity(seriesId, denominationValue);

    // Configure modal data
    const modalData: AddCashDialogData = {
      seriesName: seriesId,
      denomination: denominationValue,
      currentQuantity: currentQuantity
    };

    // Open the modal
    this.addCashModalService.openAddCashModal(modalData)
      .subscribe((result: AddCashResult | undefined) => {
        if (result) {
          this.handleAddCashResult(result, seriesId, denominationValue);
        }
      });
  }

  private getCurrentQuantity(seriesId: string, denominationValue: number): number {
    // Convert seriesId to NoteSeries enum
    const seriesMap: { [key: string]: NoteSeries } = {
      'Mandela': NoteSeries.MANDELA,
      'Big 5': NoteSeries.BIG_5,
      'Commemorative': NoteSeries.COMMEMORATIVE,
      'V6': NoteSeries.V6
    };

    const series = seriesMap[seriesId];
    const denomination = denominationValue as NoteDenomination;

    if (series && denomination) {
      return this.inventoryService.getCurrentCashQuantity(series, denomination);
    }

    return 0;
  }

  private handleAddCashResult(result: AddCashResult, seriesId: string, denominationValue: number): void {
    if (!result.success) {
      this.snackBar.open('Failed to add cash to inventory', 'Close', { duration: 3000 });
      return;
    }

    const quantity = result.added || 0;
    const totalValue = quantity * denominationValue;
    const denomination = denominationValue as NoteDenomination;

    // Update the inventory service
    let updateSuccess = false;

    // Check if this is a custom series
    if (seriesId.startsWith('custom_')) {
      updateSuccess = this.inventoryService.addCashToCustomSeries(
        seriesId,
        denomination,
        quantity,
        'Manual addition via QA Manager',
        'qa-manager'
      );
    } else {
      // Handle standard series
      const seriesMap: { [key: string]: NoteSeries } = {
        'Mandela': NoteSeries.MANDELA,
        'Big 5': NoteSeries.BIG_5,
        'Commemorative': NoteSeries.COMMEMORATIVE,
        'V6': NoteSeries.V6
      };

      const series = seriesMap[seriesId];
      if (series) {
        updateSuccess = this.inventoryService.addCash(
          series,
          denomination,
          quantity,
          'Manual addition via QA Manager',
          'qa-manager'
        );
      }
    }

    if (!updateSuccess) {
      this.snackBar.open('Failed to update inventory', 'Close', { duration: 3000 });
      return;
    }

    // Show success message
    this.snackBar.open(
      `Added ${quantity} x R${denominationValue} notes (Total: R${totalValue.toLocaleString()})`,
      'Close',
      {
        duration: 5000,
        horizontalPosition: 'center',
        verticalPosition: 'top',
        panelClass: ['success-snackbar']
      }
    );

    // Log the transaction for debugging
    console.log('Cash transaction completed:', {
      result,
      seriesId,
      denominationValue,
      updateSuccess,
      timestamp: new Date().toISOString()
    });

    // Refresh the component data to show updated inventory
    this.refreshInventoryData();
  }

  private refreshInventoryData(): void {
    console.log('Refreshing inventory data...');
    // Reload inventory data from service
    this.loadInventoryData();
    // Force change detection to update the UI
    this.cdr.detectChanges();
    console.log('Inventory data refreshed and change detection triggered');
  }

  onRemoveCash(seriesId?: string, denominationValue?: number, currentQuantity?: number): void {
    // If called without parameters, open modal with no pre-selection
    if (!seriesId || !denominationValue) {
      const modalData: RemoveCashDialogData = {};

      this.removeCashModalService.openRemoveCashModal(modalData)
        .subscribe((result: RemoveCashResult | undefined) => {
          if (result) {
            this.handleRemoveCashResult(result, '', 0);
          }
        });
      return;
    }

    // Configure modal data
    const modalData: RemoveCashDialogData = {
      seriesName: seriesId,
      denomination: denominationValue,
      currentQuantity: currentQuantity || 0
    };

    this.removeCashModalService.openRemoveCashModal(modalData)
      .subscribe((result: RemoveCashResult | undefined) => {
        if (result) {
          this.handleRemoveCashResult(result, seriesId, denominationValue);
        }
      });
  }

  private handleRemoveCashResult(result: RemoveCashResult, seriesId: string, denominationValue: number): void {
    if (!result.success) {
      this.snackBar.open('Failed to remove cash from inventory', 'Close', { duration: 3000 });
      return;
    }

    const quantity = result.removed || 0;
    const totalValue = quantity * denominationValue;
    const denomination = denominationValue as NoteDenomination;

    // Update the inventory service
    let updateSuccess = false;

    // Check if this is a custom series
    if (seriesId.startsWith('custom_')) {
      updateSuccess = this.inventoryService.removeCashFromCustomSeries(
        seriesId,
        denomination,
        quantity,
        'Manual removal via QA Manager',
        'qa-manager'
      );
    } else {
      // Handle standard series
      const seriesMap: { [key: string]: NoteSeries } = {
        'Mandela': NoteSeries.MANDELA,
        'Big 5': NoteSeries.BIG_5,
        'Commemorative': NoteSeries.COMMEMORATIVE,
        'V6': NoteSeries.V6
      };

      const series = seriesMap[seriesId];
      if (series) {
        updateSuccess = this.inventoryService.removeCash(
          series,
          denomination,
          quantity,
          'Manual removal via QA Manager',
          'qa-manager'
        );
      }
    }

    if (!updateSuccess) {
      this.snackBar.open('Failed to update inventory', 'Close', { duration: 3000 });
      return;
    }

    // Show success message
    this.snackBar.open(
      `Removed ${quantity} x R${denominationValue} notes (Total: R${totalValue.toLocaleString()})`,
      'Close',
      {
        duration: 5000,
        horizontalPosition: 'center',
        verticalPosition: 'top',
        panelClass: ['success-snackbar']
      }
    );

    // Log the transaction for debugging
    console.log('Cash removal completed:', {
      result,
      seriesId,
      denominationValue,
      updateSuccess,
      timestamp: new Date().toISOString()
    });

    // Refresh the component data to show updated inventory
    this.refreshInventoryData();
  }

  private handleGenericAddCashResult(result: AddCashResult): void {
    if (!result.success) {
      this.snackBar.open('Failed to add cash to inventory', 'Close', { duration: 3000 });
      return;
    }

    const quantity = result.added || 0;

    // Show generic success message
    this.snackBar.open(
      `Successfully added ${quantity} notes to inventory`,
      'Close',
      {
        duration: 5000,
        horizontalPosition: 'center',
        verticalPosition: 'top',
        panelClass: ['success-snackbar']
      }
    );

    // Log the transaction for debugging
    console.log('Generic cash transaction completed:', {
      result,
      timestamp: new Date().toISOString()
    });

    // Refresh inventory data
    this.refreshInventoryData();
  }

  // Methods will be redesigned from scratch

  trackByItemId(index: number, item: any): string {
    return item.id || index.toString();
  }

  viewDetails(item: any): void {
    // Navigate to detailed view or open modal
    console.log('View details for item:', item);
    this.snackBar.open(
      `Viewing details for ${this.getDenominationLabel(item.denomination)}`,
      'Close',
      { duration: 2000 }
    );
  }

  /**
   * Opens the Add Coin modal for a specific denomination
   */
  openAddCoinModal(denomination: CoinDenomination): void {
    const coinData = this.groupedCoinInventory.find(coin => coin.denomination === denomination);
    if (!coinData) {
      this.snackBar.open('Coin data not found', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    const dialogData: AddCoinDialogData = {
      denomination: denomination,
      currentQuantity: coinData.quantity
    };

    this.addCoinModalService.openAddCoinModal(dialogData).subscribe({
      next: (result: AddCoinResult | undefined) => {
        if (result) {
          this.handleCoinModalResult(result);
        }
      },
      error: (error: any) => {
        console.error('Error opening add coin modal:', error);
        this.snackBar.open('Failed to open coin modal', 'Close', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  /**
   * Handles the result from the Add Coin modal
   */
  private handleCoinModalResult(result: AddCoinResult): void {
    if (result.success && result.added) {
      this.snackBar.open(
        `Successfully added ${result.added} coins to inventory`,
        'Close',
        { duration: 4000, panelClass: ['success-snackbar'] }
      );

      // In a real application, you would refresh the inventory data here
      // For now, we'll just show the success message
    } else {
      this.snackBar.open('Failed to add coins to inventory', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
    }
  }

  /**
   * Opens the Remove Coin modal for a specific denomination
   */
  openRemoveCoinModal(denomination: CoinDenomination): void {
    const coinData = this.groupedCoinInventory.find(coin => coin.denomination === denomination);
    if (!coinData) {
      this.snackBar.open('Coin data not found', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    const dialogData: RemoveCoinDialogData = {
      seriesName: coinData.series,
      denomination: +denomination,
      currentQuantity: coinData.quantity
    };

    this.removeCoinModalService.openRemoveCoinModal(dialogData).subscribe({
      next: (result: RemoveCoinResult | undefined) => {
        if (result) {
          this.handleRemoveCoinModalResult(result, denomination);
        }
      },
      error: (error: any) => {
        console.error('Error opening remove coin modal:', error);
        this.snackBar.open('Failed to open remove coin modal', 'Close', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  /**
   * Handles the result from the Remove Coin modal
   */
  private handleRemoveCoinModalResult(result: RemoveCoinResult, denomination: CoinDenomination): void {
    if (result.success && result.removed) {
      const coinLabel = this.getCoinLabel(denomination);
      const totalValue = result.removed * +denomination;

      this.snackBar.open(
        `Successfully removed ${result.removed} x ${coinLabel} coins (Total: ${this.formatCoinValue(totalValue)})`,
        'Close',
        {
          duration: 5000,
          panelClass: ['success-snackbar'],
          horizontalPosition: 'center',
          verticalPosition: 'top'
        }
      );

      // Log the transaction for debugging
      console.log('Coin removal completed:', {
        result,
        denomination,
        coinLabel,
        totalValue,
        timestamp: new Date().toISOString()
      });

      // In a real application, you would refresh the inventory data here
      // For now, we'll just show the success message
    } else {
      this.snackBar.open('Failed to remove coins from inventory', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
    }
  }

  /**
   * Gets the coin label for display
   */
  getCoinLabel(denomination: CoinDenomination): string {
    return this.coinLabels[denomination];
  }

  /**
   * Formats coin value for display
   */
  formatCoinValue(value: number): string {
    if (value >= 1) {
      return `R${value.toFixed(0)}`;
    } else {
      return `${Math.round(value * 100)}c`;
    }
  }

  /**
   * Gets the total coin inventory value
   */
  getTotalCoinValue(): number {
    return this.groupedCoinInventory.reduce((total, coin) => total + coin.value, 0);
  }

  /**
   * Gets the total number of coins
   */
  getTotalCoins(): number {
    return this.groupedCoinInventory.reduce((total, coin) => total + coin.quantity, 0);
  }

  /**
   * Gets coins with low stock
   */
  getLowStockCoins(): CoinInventory[] {
    return this.groupedCoinInventory.filter(coin => this.getCoinStockLevel(coin) !== 'normal');
  }

  /**
   * Gets the stock level for a coin
   */
  getCoinStockLevel(coin: CoinInventory): 'normal' | 'low' | 'out-of-stock' {
    if (coin.quantity === 0) {
      return 'out-of-stock';
    } else if (coin.quantity < 500) {
      return 'low';
    } else {
      return 'normal';
    }
  }

  /**
   * Open the audit report modal
   */
  onExportAuditReport(): void {
    this.auditReportModalService.openAuditReportModal()
      .subscribe((result) => {
        // Handle modal close if needed
        if (result) {
          console.log('Audit report modal closed with result:', result);
        }
      });
  }

  onManageNoteSeries(): void {
    this.noteSeriesManagementModalService.openNoteSeriesManagementModal()
      .subscribe((result: any) => {
        if (result) {
          console.log('Note series management modal closed with result:', result);

          // If user wants to view details of a specific series, we could navigate or show details
          if (result.action === 'view-details' && result.series) {
            const seriesName = result.seriesName || result.series;
            this.snackBar.open(
              `Viewing details for ${seriesName}`,
              'Close',
              { duration: 3000 }
            );
          }

          // Refresh inventory data to reflect any changes
          this.loadInventoryData();
        } else {
          // Even if no result, refresh data in case changes were made
          this.loadInventoryData();
        }
      });
  }
}
