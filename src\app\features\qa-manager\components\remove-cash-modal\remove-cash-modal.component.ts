import { Component, Inject, OnInit, Renderer2, <PERSON>ementRef, AfterViewInit, OnDestroy } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';

import { NoteSeries, NoteDenomination, NOTE_SERIES_LABELS, DENOMINATION_LABELS } from '../../../../shared/models/inventory.model';
import { InventoryService } from '../../../../shared/services/inventory.service';

export interface RemoveCashData {
  series?: NoteSeries;
  denomination?: NoteDenomination;
  currentQuantity?: number;
}

@Component({
  selector: 'app-remove-cash-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule
  ],
  templateUrl: './remove-cash-modal.component.html',
  styleUrls: ['./remove-cash-modal.component.scss']
})
export class RemoveCashModalComponent implements OnInit, AfterViewInit, OnDestroy {
  // Series and denomination options
  availableSeries = Object.values(NoteSeries);
  availableDenominations = [NoteDenomination.R10, NoteDenomination.R20, NoteDenomination.R50, NoteDenomination.R100, NoteDenomination.R200];

  // Form state
  selectedSeries: NoteSeries | null = null;
  selectedDenomination: NoteDenomination | null = null;
  batches: number = 0;
  singles: number = 0;
  reason: string = '';

  // Current inventory data
  currentQuantity: number = 0;
  currentBatches: number = 0;
  currentSingles: number = 0;

  // Calculated values
  totalQuantity: number = 0;
  remainingQuantity: number = 0;
  remainingBatches: number = 0;
  remainingSingles: number = 0;
  totalValue: number = 0;

  // Labels for display
  NOTE_SERIES_LABELS = NOTE_SERIES_LABELS;
  DENOMINATION_LABELS = DENOMINATION_LABELS;

  constructor(
    public dialogRef: MatDialogRef<RemoveCashModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: RemoveCashData,
    private snackBar: MatSnackBar,
    private renderer: Renderer2,
    private elementRef: ElementRef,
    private inventoryService: InventoryService
  ) {
    // Pre-populate from dialog data
    if (data?.series) {
      this.selectedSeries = data.series;
    }
    if (data?.denomination) {
      this.selectedDenomination = data.denomination;
    }
    if (data?.currentQuantity !== undefined) {
      this.currentQuantity = data.currentQuantity;
      this.updateCurrentInventoryBreakdown();
    }
  }

  ngOnInit(): void {
    // If we have pre-selected series and denomination, load current inventory
    if (this.selectedSeries && this.selectedDenomination) {
      this.loadCurrentInventory();
    }
  }

  ngAfterViewInit(): void {
    // Backdrop blur is now handled entirely by CSS for smoother transitions
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  onSeriesChange(): void {
    this.selectedDenomination = null;
    this.resetQuantities();
    this.loadCurrentInventory();
  }

  onDenominationChange(): void {
    this.resetQuantities();
    this.loadCurrentInventory();
  }

  private loadCurrentInventory(): void {
    if (this.selectedSeries && this.selectedDenomination) {
      this.currentQuantity = this.inventoryService.getCurrentCashQuantity(this.selectedSeries, this.selectedDenomination);
      this.updateCurrentInventoryBreakdown();
      this.calculateValues();
    }
  }

  private updateCurrentInventoryBreakdown(): void {
    this.currentBatches = Math.floor(this.currentQuantity / 100);
    this.currentSingles = this.currentQuantity % 100;
  }

  private resetQuantities(): void {
    this.batches = 0;
    this.singles = 0;
    this.currentQuantity = 0;
    this.currentBatches = 0;
    this.currentSingles = 0;
    this.calculateValues();
  }

  adjustBatches(delta: number): void {
    const newBatches = Math.max(0, this.batches + delta);
    const maxBatches = Math.floor(this.currentQuantity / 100);
    this.batches = Math.min(newBatches, maxBatches);
    this.onQuantityChange();
  }

  adjustSingles(delta: number): void {
    const newSingles = Math.max(0, this.singles + delta);
    const maxSingles = this.currentQuantity - (this.batches * 100);
    this.singles = Math.min(newSingles, maxSingles);
    this.onQuantityChange();
  }

  onQuantityChange(): void {
    // Ensure we don't exceed available inventory
    const maxBatches = Math.floor(this.currentQuantity / 100);
    this.batches = Math.min(this.batches, maxBatches);

    const maxSingles = this.currentQuantity - (this.batches * 100);
    this.singles = Math.min(this.singles, maxSingles);

    this.calculateValues();
  }

  private calculateValues(): void {
    this.totalQuantity = (this.batches * 100) + this.singles;
    this.remainingQuantity = this.currentQuantity - this.totalQuantity;
    this.remainingBatches = Math.floor(this.remainingQuantity / 100);
    this.remainingSingles = this.remainingQuantity % 100;

    if (this.selectedDenomination) {
      this.totalValue = this.totalQuantity * this.selectedDenomination;
    }
  }

  isFormValid(): boolean {
    return !!(
      this.selectedSeries &&
      this.selectedDenomination &&
      this.totalQuantity > 0 &&
      this.totalQuantity <= this.currentQuantity
    );
  }

  getSelectedSeriesLabel(): string {
    return this.selectedSeries ? NOTE_SERIES_LABELS[this.selectedSeries] : '';
  }

  getSelectedDenominationLabel(): string {
    return this.selectedDenomination ? DENOMINATION_LABELS[this.selectedDenomination] : '';
  }

  onRemoveCash(): void {
    if (!this.isFormValid()) {
      this.snackBar.open('Please complete all required fields', 'Close', { duration: 3000 });
      return;
    }

    try {
      // Use provided reason or default to "Manual inventory removal"
      const reasonText = this.reason.trim() || 'Manual inventory removal';

      const success = this.inventoryService.removeCash(
        this.selectedSeries!,
        this.selectedDenomination!,
        this.totalQuantity,
        reasonText
      );

      if (success) {
        const quantityDescription = this.batches > 0 && this.singles > 0
          ? `${this.batches} batches + ${this.singles} singles`
          : this.batches > 0
            ? `${this.batches} batches`
            : `${this.singles} singles`;

        this.snackBar.open(
          `Successfully removed ${quantityDescription} (${this.totalQuantity} notes) x ${DENOMINATION_LABELS[this.selectedDenomination!]}`,
          'Close',
          { duration: 4000 }
        );

        this.dialogRef.close({ success: true, removed: this.totalQuantity });
      } else {
        this.snackBar.open('Failed to remove cash inventory', 'Close', { duration: 3000 });
      }
    } catch (error) {
      console.error('Error removing cash:', error);
      this.snackBar.open('Error removing cash inventory', 'Close', { duration: 3000 });
    }
  }

  onCancel(): void {
    this.dialogRef.close({ success: false });
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  }
}
