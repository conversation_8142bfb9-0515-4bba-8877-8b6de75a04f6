// ===== NOTE SERIES MANAGEMENT MODAL STYLES =====

.note-series-management-modal {
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  width: 100%;
  background: var(--absa-white);
  border-radius: 12px;
  overflow: hidden;

  // ===== MODAL HEADER =====
  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, var(--absa-red) 0%, #c41e3a 100%);
    color: var(--absa-white);

    .header-content {
      display: flex;
      align-items: center;
      gap: 1rem;

      .header-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        backdrop-filter: blur(10px);

        mat-icon {
          font-size: 24px;
          width: 24px;
          height: 24px;
        }
      }

      .header-text {
        h2 {
          margin: 0;
          font-size: 1.5rem;
          font-weight: 600;
          line-height: 1.2;
        }

        p {
          margin: 0.25rem 0 0 0;
          font-size: 0.875rem;
          opacity: 0.9;
          line-height: 1.4;
        }
      }
    }

    .close-button {
      color: var(--absa-white);
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }

  // ===== MODAL CONTENT =====
  .modal-content {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 4rem 2rem;
      text-align: center;

      .loading-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        color: var(--absa-red);
        margin-bottom: 1rem;
        animation: spin 2s linear infinite;
      }

      p {
        color: var(--absa-gray-600);
        font-size: 1rem;
        margin: 0;
      }
    }

    .content-container {
      max-width: 1200px;
      margin: 0 auto;
    }
  }

  // ===== ACTION BAR =====
  .action-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--absa-gray-200);

    .action-info {
      h3 {
        margin: 0 0 0.25rem 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--absa-gray-900);
      }

      p {
        margin: 0;
        color: var(--absa-gray-600);
        font-size: 0.875rem;
      }
    }

    .add-series-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      font-weight: 500;
    }
  }

  // ===== ADD SERIES FORM =====
  .add-series-form {
    margin-bottom: 2rem;
    border: 2px solid var(--absa-red-100);
    border-radius: 12px;
    overflow: hidden;

    mat-card-header {
      background: var(--absa-red-50);
      padding: 1.5rem;

      .mat-mdc-card-avatar {
        background: var(--absa-red);
        color: var(--absa-white);
      }
    }

    .form-grid {
      display: grid;
      gap: 1.5rem;
      margin-top: 1rem;

      .full-width {
        width: 100%;
      }
    }

    mat-card-actions {
      padding: 1rem 1.5rem;
      background: var(--absa-gray-50);
    }
  }

  // ===== SERIES GRID =====
  .series-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;

    .series-card {
      border-radius: 12px;
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 2px solid transparent;

      &.active {
        border-color: var(--absa-success);
        box-shadow: 0 4px 12px rgba(34, 197, 94, 0.15);

        .series-avatar {
          background: var(--absa-success);
          color: var(--absa-white);
        }
      }

      &.inactive {
        border-color: var(--absa-gray-300);
        opacity: 0.7;

        .series-avatar {
          background: var(--absa-gray-400);
          color: var(--absa-white);
        }
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }

      mat-card-header {
        position: relative;
        padding: 1.5rem;

        .series-status {
          position: absolute;
          top: 1rem;
          right: 1rem;

          .status-badge {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;

            &.active {
              background: var(--absa-success-100);
              color: var(--absa-success-700);
            }

            &.inactive {
              background: var(--absa-gray-100);
              color: var(--absa-gray-600);
            }

            mat-icon {
              font-size: 14px;
              width: 14px;
              height: 14px;
            }
          }
        }
      }

      .series-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1.5rem;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 1rem;
          background: var(--absa-gray-50);
          border-radius: 8px;

          .stat-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            background: var(--absa-white);
            border-radius: 8px;
            color: var(--absa-red);
          }

          .stat-content {
            .stat-value {
              font-size: 1.125rem;
              font-weight: 600;
              color: var(--absa-gray-900);
              line-height: 1.2;
            }

            .stat-label {
              font-size: 0.75rem;
              color: var(--absa-gray-600);
              margin-top: 0.125rem;
            }
          }
        }
      }

      .denominations-section {
        h4 {
          margin: 0 0 0.75rem 0;
          font-size: 0.875rem;
          font-weight: 600;
          color: var(--absa-gray-700);
        }

        .denomination-chips {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;

          .denomination-chip {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            background: var(--absa-red-100);
            color: var(--absa-red-700);
            border-radius: 16px;
            font-size: 0.75rem;
            font-weight: 500;
          }
        }
      }

      .no-denominations {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem;
        background: var(--absa-gray-50);
        border-radius: 8px;
        color: var(--absa-gray-600);
        font-size: 0.875rem;

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }
    }
  }

  // ===== EMPTY STATE =====
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;

    .empty-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      color: var(--absa-gray-400);
      margin-bottom: 1.5rem;
    }

    h3 {
      margin: 0 0 0.5rem 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--absa-gray-700);
    }

    p {
      margin: 0 0 2rem 0;
      color: var(--absa-gray-600);
      font-size: 1rem;
      max-width: 400px;
    }
  }

  // ===== MODAL FOOTER =====
  .modal-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    background: var(--absa-gray-50);
    border-top: 1px solid var(--absa-gray-200);

    .footer-info {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: var(--absa-gray-600);
      font-size: 0.875rem;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}

// ===== ANIMATIONS =====
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 768px) {
  .note-series-management-modal {
    .modal-header {
      padding: 1rem 1.5rem;

      .header-content {
        .header-icon {
          width: 40px;
          height: 40px;

          mat-icon {
            font-size: 20px;
            width: 20px;
            height: 20px;
          }
        }

        .header-text {
          h2 {
            font-size: 1.25rem;
          }

          p {
            font-size: 0.8rem;
          }
        }
      }
    }

    .modal-content {
      padding: 1.5rem;
    }

    .action-bar {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;

      .add-series-btn {
        align-self: stretch;
        justify-content: center;
      }
    }

    .series-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .modal-footer {
      padding: 1rem 1.5rem;
      flex-direction: column;
      gap: 1rem;

      .footer-info {
        text-align: center;
      }
    }
  }
}
