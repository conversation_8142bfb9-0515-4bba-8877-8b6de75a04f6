import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';


import { NoteSeries, NoteDenomination, NOTE_SERIES_LABELS, DENOMINATION_LABELS, NoteSeriesInfo } from '../../../../shared/models/inventory.model';
import { InventoryService } from '../../../../shared/services/inventory.service';

export interface NoteSeriesManagementData {
  // Optional data can be passed to the modal
}

export interface NoteSeriesItem {
  id: string;
  name: string;
  description: string;
  icon: string;
  totalNotes: number;
  totalValue: number;
  denominations: NoteDenomination[];
  isActive: boolean;
  isPredefined: boolean;
}

@Component({
  selector: 'app-note-series-management-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatSnackBarModule,
    MatCardModule,
    MatDividerModule,
    MatTooltipModule
  ],
  templateUrl: './note-series-management-modal.component.html',
  styleUrls: ['./note-series-management-modal.component.scss']
})
export class NoteSeriesManagementModalComponent implements OnInit {
  noteSeriesList: NoteSeriesItem[] = [];
  isLoading = false;

  // Add new series form
  showAddForm = false;
  newSeriesName = '';
  newSeriesDescription = '';
  selectedDenominations: NoteDenomination[] = [];

  availableDenominations = Object.values(NoteDenomination)
    .filter(d => typeof d === 'number')
    .map(denom => ({
      value: denom as NoteDenomination,
      label: DENOMINATION_LABELS[denom as NoteDenomination]
    }));

  constructor(
    private dialogRef: MatDialogRef<NoteSeriesManagementModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: NoteSeriesManagementData,
    private inventoryService: InventoryService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadNoteSeriesData();
  }

  private loadNoteSeriesData(): void {
    this.isLoading = true;

    try {
      const allSeries = this.inventoryService.getAllNoteSeries();
      const cashInventory = this.inventoryService.getCashInventory();

      // Calculate totals for each series
      this.noteSeriesList = allSeries.map(series => {
        let totalNotes = 0;
        let totalValue = 0;
        const activeDenominations: NoteDenomination[] = [];

        // Calculate totals from cash inventory
        cashInventory.forEach(item => {
          const itemSeriesId = series.isPredefined ? item.noteSeries : item.id.split('-')[1];
          if (itemSeriesId === series.id) {
            totalNotes += item.quantity;
            totalValue += item.value;
            if (item.quantity > 0 && !activeDenominations.includes(item.denomination)) {
              activeDenominations.push(item.denomination);
            }
          }
        });

        return {
          id: series.id,
          name: series.name,
          description: series.description,
          icon: series.icon || this.getSeriesIcon(series.id),
          totalNotes,
          totalValue,
          denominations: activeDenominations.length > 0 ? activeDenominations.sort((a, b) => a - b) : series.denominations,
          isActive: totalNotes > 0,
          isPredefined: series.isPredefined
        };
      }).sort((a, b) => a.name.localeCompare(b.name));

      this.isLoading = false;
    } catch (error) {
      console.error('Error loading note series data:', error);
      this.isLoading = false;
      this.snackBar.open('Error loading note series data', 'Close', { duration: 3000 });
    }
  }

  private getSeriesIcon(seriesId: string): string {
    const predefinedIcons = {
      [NoteSeries.MANDELA]: 'account_balance',
      [NoteSeries.BIG_5]: 'nature',
      [NoteSeries.COMMEMORATIVE]: 'star',
      [NoteSeries.V6]: 'new_releases'
    };
    return predefinedIcons[seriesId as NoteSeries] || 'category';
  }

  private getSeriesDescription(series: NoteSeries): string {
    const descriptions = {
      [NoteSeries.MANDELA]: 'Standard circulation notes featuring Nelson Mandela',
      [NoteSeries.BIG_5]: 'Wildlife themed series featuring the Big 5 animals',
      [NoteSeries.COMMEMORATIVE]: 'Special edition commemorative notes',
      [NoteSeries.V6]: 'Latest series design with enhanced security features'
    };
    return descriptions[series] || 'Note series';
  }

  onShowAddForm(): void {
    this.showAddForm = true;
    this.newSeriesName = '';
    this.newSeriesDescription = '';
    this.selectedDenominations = [];
  }

  onCancelAdd(): void {
    this.showAddForm = false;
    this.newSeriesName = '';
    this.newSeriesDescription = '';
    this.selectedDenominations = [];
  }

  onAddNewSeries(): void {
    if (!this.newSeriesName.trim()) {
      this.snackBar.open('Please enter a series name', 'Close', { duration: 3000 });
      return;
    }

    if (this.selectedDenominations.length === 0) {
      this.snackBar.open('Please select at least one denomination', 'Close', { duration: 3000 });
      return;
    }

    const success = this.inventoryService.addCustomNoteSeries(
      this.newSeriesName.trim(),
      this.newSeriesDescription.trim() || `Custom note series: ${this.newSeriesName.trim()}`,
      this.selectedDenominations,
      'user'
    );

    if (success) {
      this.snackBar.open(
        `Successfully added "${this.newSeriesName.trim()}" series`,
        'Close',
        { duration: 3000 }
      );
      this.loadNoteSeriesData(); // Refresh the data
      this.onCancelAdd();
    } else {
      this.snackBar.open(
        'Failed to add series. Series name may already exist.',
        'Close',
        { duration: 4000 }
      );
    }
  }

  onRemoveSeries(seriesItem: NoteSeriesItem): void {
    if (seriesItem.totalNotes > 0) {
      this.snackBar.open(
        'Cannot remove series with existing inventory. Please remove all notes first.',
        'Close',
        { duration: 4000 }
      );
      return;
    }

    if (seriesItem.isPredefined) {
      this.snackBar.open(
        'Cannot remove predefined series. Only custom series can be removed.',
        'Close',
        { duration: 4000 }
      );
      return;
    }

    // Confirm removal
    if (confirm(`Are you sure you want to remove "${seriesItem.name}" series? This action cannot be undone.`)) {
      const success = this.inventoryService.removeCustomNoteSeries(seriesItem.id);

      if (success) {
        this.snackBar.open(
          `Successfully removed "${seriesItem.name}" series`,
          'Close',
          { duration: 3000 }
        );
        this.loadNoteSeriesData(); // Refresh the data
      } else {
        this.snackBar.open(
          'Failed to remove series. Please try again.',
          'Close',
          { duration: 4000 }
        );
      }
    }
  }

  onViewSeriesDetails(seriesItem: NoteSeriesItem): void {
    // Close this modal and potentially open a detailed view
    this.dialogRef.close({ action: 'view-details', series: seriesItem.id, seriesName: seriesItem.name });
  }

  formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(value);
  }

  formatNumber(value: number): string {
    return new Intl.NumberFormat('en-ZA').format(value);
  }

  onClose(): void {
    this.dialogRef.close();
  }
}
