// ===== RESPONSIVE BREAKPOINTS =====
$mobile-small: 320px;
$mobile: 480px;
$tablet: 768px;
$desktop: 1024px;
$desktop-large: 1200px;

// ===== COLOR VARIABLES =====
// Use CSS custom properties that are defined in global styles
:host {
  --primary-color: #059669;
  --primary-dark: #047857;
}

// ===== RESPONSIVE MIXINS =====
@mixin mobile-small {
  @media (max-width: #{$mobile-small - 1px}) {
    @content;
  }
}

@mixin mobile {
  @media (max-width: #{$mobile - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (max-width: #{$tablet - 1px}) {
    @content;
  }
}

// ===== AUDIT REPORT MODAL =====
.audit-report-modal {
  width: 95vw;
  max-width: 1400px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  background: var(--absa-white);
  border-radius: 16px;
  overflow: hidden;

  @include tablet {
    width: 98vw;
    max-height: 95vh;
  }

  @include mobile {
    width: 100vw;
    max-height: 100vh;
    border-radius: 0;
  }
}

// ===== MODAL HEADER =====
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: var(--absa-white);
  position: relative;

  @include tablet {
    padding: 1rem 1.5rem;
  }

  @include mobile {
    padding: 1rem;
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: 1rem;

    .header-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      backdrop-filter: blur(10px);

      @include mobile {
        width: 40px;
        height: 40px;
      }

      mat-icon {
        font-size: 24px;
        width: 24px;
        height: 24px;

        @include mobile {
          font-size: 20px;
          width: 20px;
          height: 20px;
        }
      }
    }

    .header-text {
      h2 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 700;

        @include tablet {
          font-size: 1.25rem;
        }

        @include mobile {
          font-size: 1.125rem;
        }
      }

      .header-subtitle {
        margin: 0.25rem 0 0 0;
        font-size: 0.875rem;
        opacity: 0.9;

        @include mobile {
          font-size: 0.75rem;
        }
      }
    }
  }

  .close-btn {
    color: var(--absa-white);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

// ===== LOADING STATE =====
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;

  .loading-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: var(--primary-color);
    margin-bottom: 1rem;
    animation: spin 2s linear infinite;
  }

  p {
    color: var(--absa-gray-600);
    font-size: 1rem;
    margin: 0;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// ===== SPINNING ANIMATION FOR EXPORT BUTTON =====
.spinning {
  animation: spin 1s linear infinite;
}

// ===== MODAL CONTENT =====
.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

// ===== SUMMARY SECTION =====
.summary-section {
  padding: 2rem;
  background: var(--absa-gray-50);

  @include tablet {
    padding: 1.5rem;
  }

  @include mobile {
    padding: 1rem;
  }

  .section-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 0 1.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--absa-gray-900);

    mat-icon {
      color: var(--primary-color);
    }
  }

  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;

    @include tablet {
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: 1rem;
    }

    @include mobile {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }

  .summary-card {
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
    }

    &.warning {
      border-left: 4px solid #f59e0b;
    }

    ::ng-deep .mat-mdc-card-header {
      padding-bottom: 0.5rem;

      .mat-mdc-card-avatar {
        background: var(--primary-color);
        color: var(--absa-white);
        border-radius: 8px;
      }

      .mat-mdc-card-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--absa-gray-700);
      }
    }

    ::ng-deep .mat-mdc-card-content {
      .metric-value {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--absa-gray-900);
        margin-bottom: 0.25rem;

        @include mobile {
          font-size: 1.5rem;
        }
      }

      .metric-subtitle {
        font-size: 0.75rem;
        color: var(--absa-gray-500);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .metric-breakdown {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        font-size: 0.75rem;
        color: var(--absa-gray-600);

        span {
          display: flex;
          justify-content: space-between;
        }
      }
    }
  }
}

// ===== DETAILS SECTION =====
.details-section {
  padding: 0 2rem 2rem 2rem;

  @include tablet {
    padding: 0 1.5rem 1.5rem 1.5rem;
  }

  @include mobile {
    padding: 0 1rem 1rem 1rem;
  }

  .report-tabs {
    ::ng-deep .mat-mdc-tab-group {
      .mat-mdc-tab-header {
        border-bottom: 1px solid var(--absa-gray-200);

        .mat-mdc-tab-label-container {
          .mat-mdc-tab {
            min-width: 120px;
            padding: 0 1rem;

            @include mobile {
              min-width: 80px;
              padding: 0 0.5rem;
            }

            .mat-mdc-tab-label-content {
              display: flex;
              align-items: center;
              gap: 0.5rem;
              font-weight: 500;

              mat-icon {
                font-size: 18px;
                width: 18px;
                height: 18px;

                @include mobile {
                  font-size: 16px;
                  width: 16px;
                  height: 16px;
                }
              }
            }
          }
        }
      }
    }
  }

  .tab-content {
    padding: 2rem 0;

    @include tablet {
      padding: 1.5rem 0;
    }

    @include mobile {
      padding: 1rem 0;
    }
  }
}

// ===== SERIES BREAKDOWN =====
.series-breakdown {
  margin-bottom: 2rem;

  &:last-child {
    margin-bottom: 0;
  }

  .series-card {
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    ::ng-deep .mat-mdc-card-header {
      background: var(--absa-gray-50);
      border-radius: 12px 12px 0 0;

      .mat-mdc-card-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--absa-gray-900);
      }

      .mat-mdc-card-subtitle {
        color: var(--absa-gray-600);
        font-weight: 500;
      }
    }

    .series-summary {
      display: flex;
      gap: 2rem;
      margin-bottom: 1.5rem;
      padding: 1rem;
      background: var(--absa-gray-50);
      border-radius: 8px;

      @include mobile {
        flex-direction: column;
        gap: 0.5rem;
      }

      .summary-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .label {
          font-weight: 500;
          color: var(--absa-gray-700);
        }

        .value {
          font-weight: 600;
          color: var(--absa-gray-900);
        }
      }
    }
  }
}

// ===== TABLES =====
.denomination-table,
.coins-table,
.low-stock-table,
.breakdown-table {
  width: 100%;
  margin-top: 1rem;

  ::ng-deep {
    .mat-mdc-table {
      background: var(--absa-white);
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .mat-mdc-header-row {
      background: var(--absa-gray-100);

      .mat-mdc-header-cell {
        font-weight: 600;
        color: var(--absa-gray-700);
        border-bottom: 1px solid var(--absa-gray-200);
      }
    }

    .mat-mdc-row {
      &:hover {
        background: var(--absa-gray-50);
      }

      .mat-mdc-cell {
        border-bottom: 1px solid var(--absa-gray-100);
      }
    }
  }
}

// ===== BADGES =====
.stock-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.normal {
    background: #d1fae5;
    color: #065f46;
  }

  &.low {
    background: #fef3c7;
    color: #92400e;
  }

  &.out-of-stock {
    background: #fee2e2;
    color: #991b1b;
  }
}

.type-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;

  &.note {
    background: #dbeafe;
    color: #1e40af;
  }

  &.coin {
    background: #fef3c7;
    color: #92400e;
  }

  mat-icon {
    font-size: 14px;
    width: 14px;
    height: 14px;
  }
}

// ===== ALERT MESSAGE =====
.alert-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  background: var(--absa-gray-50);
  border-radius: 12px;

  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: #10b981;
    margin-bottom: 1rem;
  }

  h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--absa-gray-900);
  }

  p {
    margin: 0;
    color: var(--absa-gray-600);
  }
}

// ===== MODAL ACTIONS =====
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  background: var(--absa-gray-50);
  border-top: 1px solid var(--absa-gray-200);

  @include tablet {
    padding: 1rem 1.5rem;
  }

  @include mobile {
    padding: 1rem;
    flex-direction: column-reverse;
    gap: 0.75rem;
  }

  .cancel-btn {
    color: var(--absa-gray-600);
    border-color: var(--absa-gray-300);

    &:hover {
      background: var(--absa-gray-100);
    }
  }

  .download-btn {
    background: var(--primary-color);
    color: var(--absa-white);

    &:hover {
      background: var(--primary-dark);
    }

    &:disabled {
      background: var(--absa-gray-300);
      color: var(--absa-gray-500);
    }
  }
}
