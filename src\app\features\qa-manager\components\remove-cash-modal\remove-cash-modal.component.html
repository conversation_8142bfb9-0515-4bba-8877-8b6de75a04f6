<div class="remove-cash-modal-container">
  <!-- Modern Header with Gradient -->
  <div class="modal-header">
    <div class="header-content">
      <div class="header-icon">
        <mat-icon>money_off</mat-icon>
      </div>
      <div class="header-text">
        <h2>Remove Cash Inventory</h2>
        <p>Manage your cash reserves with precision</p>
      </div>
    </div>
    <button mat-icon-button class="close-button" (click)="onCancel()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Main Content Area -->
  <div class="modal-content">
    <form #removeCashForm="ngForm" class="remove-cash-form">

      <!-- Step 1: Series Selection -->
      <div class="form-section series-selection">
        <div class="section-header">
          <mat-icon class="step-icon">category</mat-icon>
          <h3>Select Note Series</h3>
          <span class="pre-selected-badge" *ngIf="selectedSeries && data?.series">
            <mat-icon>check_circle</mat-icon>
            Pre-selected
          </span>
        </div>
        <div class="series-grid">
          <div *ngFor="let series of availableSeries"
               class="series-card"
               [class.selected]="selectedSeries === series"
               (click)="selectedSeries = series; onSeriesChange()">
            <div class="series-icon">
              <mat-icon>{{ series === availableSeries[0] ? 'looks_6' : 'looks_7' }}</mat-icon>
            </div>
            <div class="series-info">
              <h4>{{ NOTE_SERIES_LABELS[series] }}</h4>
              <p>Latest series design</p>
            </div>
            <div class="selection-indicator" *ngIf="selectedSeries === series">
              <mat-icon>check_circle</mat-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 2: Denomination Selection -->
      <div class="form-section denomination-section" *ngIf="selectedSeries">
        <div class="section-header">
          <mat-icon class="step-icon">payments</mat-icon>
          <h3>Choose Denomination</h3>
        </div>
        <div class="denomination-grid">
          <div class="denomination-card"
               *ngFor="let denomination of availableDenominations"
               [class.selected]="selectedDenomination === denomination"
               (click)="selectedDenomination = denomination; onDenominationChange()">
            <div class="denomination-icon">
              <mat-icon>{{ +denomination >= 100 ? 'credit_card' : 'receipt' }}</mat-icon>
            </div>
            <div class="denomination-info">
              <h4>{{ DENOMINATION_LABELS[denomination] }}</h4>
              <p>{{ +denomination >= 100 ? 'High value note' : 'Standard note' }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 3: Quantity Input -->
      <div class="form-section quantity-section" *ngIf="selectedDenomination && currentQuantity > 0">
        <div class="section-header">
          <mat-icon class="step-icon">calculate</mat-icon>
          <h3>Specify Removal Quantity</h3>
        </div>
        <div class="quantity-controls">
          <div class="quantity-card batches-card">
            <div class="card-header">
              <mat-icon>inventory_2</mat-icon>
              <h4>Batches</h4>
              <span class="helper-text">100 notes each</span>
            </div>
            <div class="input-container">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustBatches(-1)" [disabled]="batches <= 0">
                <mat-icon>remove</mat-icon>
              </button>
              <input type="number"
                     [(ngModel)]="batches"
                     name="batches"
                     min="0"
                     [max]="currentBatches"
                     class="quantity-input"
                     (input)="onQuantityChange()">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustBatches(1)" [disabled]="batches >= currentBatches">
                <mat-icon>add</mat-icon>
              </button>
            </div>
            <div class="quantity-info">
              <span class="available">Available: {{ currentBatches }} batches</span>
            </div>
          </div>

          <div class="quantity-card singles-card">
            <div class="card-header">
              <mat-icon>receipt_long</mat-icon>
              <h4>Singles</h4>
              <span class="helper-text">Individual notes</span>
            </div>
            <div class="input-container">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustSingles(-1)" [disabled]="singles <= 0">
                <mat-icon>remove</mat-icon>
              </button>
              <input type="number"
                     [(ngModel)]="singles"
                     name="singles"
                     min="0"
                     [max]="currentQuantity - (batches * 100)"
                     class="quantity-input"
                     (input)="onQuantityChange()">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustSingles(1)" [disabled]="singles >= (currentQuantity - (batches * 100))">
                <mat-icon>add</mat-icon>
              </button>
            </div>
            <div class="quantity-info">
              <span class="available">Available: {{ currentQuantity - (batches * 100) }} singles</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 4: Reason (Optional) -->
      <div class="form-section reason-section" *ngIf="selectedDenomination && currentQuantity > 0">
        <div class="section-header">
          <mat-icon class="step-icon">description</mat-icon>
          <h3>Reason (Optional)</h3>
        </div>
        <mat-form-field class="reason-field" appearance="outline">
          <mat-label>Reason for removal</mat-label>
          <input matInput
                 [(ngModel)]="reason"
                 name="reason"
                 placeholder="e.g., Damaged notes, End of day reconciliation"
                 maxlength="200">
          <mat-hint>Optional: Provide a reason for this removal</mat-hint>
        </mat-form-field>
      </div>
      <!-- Current Inventory Display -->
      <div class="current-inventory-section" *ngIf="selectedSeries && selectedDenomination">
        <div class="section-header">
          <mat-icon class="step-icon">inventory</mat-icon>
          <h3>Current Inventory</h3>
        </div>
        <div class="inventory-display">
          <div class="inventory-card">
            <div class="inventory-header">
              <mat-icon>account_balance_wallet</mat-icon>
              <h4>Available Cash</h4>
            </div>
            <div class="inventory-details">
              <div class="detail-item">
                <span class="detail-label">Total Notes:</span>
                <span class="detail-value">{{ currentQuantity | number }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Batches:</span>
                <span class="detail-value">{{ currentBatches }} ({{ currentBatches * 100 }} notes)</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">Singles:</span>
                <span class="detail-value">{{ currentSingles }} notes</span>
              </div>
              <div class="detail-item total-value">
                <span class="detail-label">Total Value:</span>
                <span class="detail-value">{{ formatCurrency(currentQuantity * selectedDenomination) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Summary Section -->
      <div class="summary-section" *ngIf="totalQuantity > 0 && selectedDenomination">
        <div class="summary-card">
          <div class="summary-header">
            <mat-icon>summarize</mat-icon>
            <h4>Removal Summary</h4>
          </div>
          <div class="summary-content">
            <div class="summary-row">
              <span class="summary-label">Removing:</span>
              <span class="summary-value">{{ totalQuantity }} notes ({{ formatCurrency(totalValue) }})</span>
            </div>
              <div class="summary-row remaining">
                <span class="summary-label">Remaining:</span>
                <span class="summary-value">{{ remainingQuantity }} notes ({{ formatCurrency(remainingQuantity * selectedDenomination) }})</span>
              </div>
            </div>

            <div class="breakdown-section" *ngIf="batches > 0 || singles > 0">
              <div class="breakdown-title">
                <mat-icon>analytics</mat-icon>
                <span>Breakdown</span>
              </div>
              <div class="breakdown-items">
                <div class="breakdown-item" *ngIf="batches > 0">
                  <span class="breakdown-label">{{ batches }} batch{{ batches !== 1 ? 'es' : '' }}</span>
                  <span class="breakdown-value">{{ batches * 100 }} notes</span>
                </div>
                <div class="breakdown-item" *ngIf="singles > 0">
                  <span class="breakdown-label">{{ singles }} single{{ singles !== 1 ? 's' : '' }}</span>
                  <span class="breakdown-value">{{ singles }} notes</span>
                </div>
              </div>
            </div>
          </div>
        </div>

      <!-- No Inventory Message -->
      <div class="no-inventory-section" *ngIf="selectedDenomination && currentQuantity === 0">
        <div class="no-inventory-card">
          <mat-icon>inventory_2</mat-icon>
          <h3>No Inventory Available</h3>
          <p>There are currently no {{ getSelectedDenominationLabel() }} notes available for removal.</p>
        </div>
      </div>
    </form>
  </div>

  <!-- Action Buttons -->
  <div class="modal-actions">
    <button mat-stroked-button class="cancel-btn" (click)="onCancel()">
      <mat-icon>cancel</mat-icon>
      Cancel
    </button>
    <button mat-raised-button
            class="remove-btn"
            [disabled]="!isFormValid()"
            (click)="onRemoveCash()">
      <mat-icon>remove_circle</mat-icon>
      Remove from Inventory
    </button>
  </div>
</div>
