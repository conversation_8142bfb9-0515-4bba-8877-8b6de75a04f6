import { Component, Inject, OnInit, Renderer2, <PERSON>ement<PERSON>ef, AfterViewInit, OnDestroy } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';

import {
  CoinSeries,
  CoinDenomination,
  COIN_SERIES_LABELS,
  COIN_DENOMINATION_LABELS,
  COIN_BATCH_CONFIG,
  COIN_BATCH_VALUES
} from '../../../../shared/models/inventory.model';
import { InventoryService } from '../../../../shared/services/inventory.service';

export interface AddCoinData {
  series?: CoinSeries;
  denomination?: CoinDenomination;
}

export interface AddCoinDialogData {
  series?: CoinSeries;
  denomination?: CoinDenomination;
  currentQuantity?: number;
}

export interface AddCoinResult {
  success: boolean;
  added?: number;
}



@Component({
  selector: 'app-add-coin-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule
  ],
  templateUrl: './add-coin-modal.component.html',
  styleUrls: ['./add-coin-modal.component.scss']
})
export class AddCoinModalComponent implements OnInit, AfterViewInit, OnDestroy {
  // Series and denomination options
  availableSeries = Object.values(CoinSeries);
  availableDenominations = [CoinDenomination.R5, CoinDenomination.R2, CoinDenomination.R1, CoinDenomination.C50, CoinDenomination.C20, CoinDenomination.C10];

  // Form state
  selectedSeries: CoinSeries | null = null;
  selectedDenomination: CoinDenomination | null = null;
  batches: number = 0;
  singles: number = 0;
  reason: string = '';

  // Calculated values
  totalQuantity: number = 0;
  totalValue: number = 0;

  // Labels for display
  COIN_SERIES_LABELS = COIN_SERIES_LABELS;
  COIN_DENOMINATION_LABELS = COIN_DENOMINATION_LABELS;
  COIN_BATCH_CONFIG = COIN_BATCH_CONFIG;
  COIN_BATCH_VALUES = COIN_BATCH_VALUES;

  constructor(
    public dialogRef: MatDialogRef<AddCoinModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AddCoinDialogData,
    private snackBar: MatSnackBar,
    private renderer: Renderer2,
    private elementRef: ElementRef,
    private inventoryService: InventoryService
  ) {
    // Pre-populate from dialog data - only denomination, user should choose series
    if (data?.denomination) {
      this.selectedDenomination = data.denomination;
    }
  }

  ngOnInit(): void {
    // Component initialization
    // Calculate values if denomination is pre-selected and user selects a series
    if (this.selectedSeries && this.selectedDenomination) {
      this.calculateValues();
    }
  }

  ngAfterViewInit(): void {
    // Backdrop blur is now handled entirely by CSS for smoother transitions
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  onSeriesChange(): void {
    this.selectedDenomination = null;
    this.resetQuantities();
  }

  onDenominationChange(): void {
    this.resetQuantities();
  }

  private resetQuantities(): void {
    this.batches = 0;
    this.singles = 0;
    this.calculateValues();
  }

  adjustBatches(delta: number): void {
    this.batches = Math.max(0, this.batches + delta);
    this.onQuantityChange();
  }

  adjustSingles(delta: number): void {
    this.singles = Math.max(0, this.singles + delta);
    this.onQuantityChange();
  }

  onQuantityChange(): void {
    this.calculateValues();
  }

  private calculateValues(): void {
    if (this.selectedDenomination) {
      const coinsPerBatch = COIN_BATCH_CONFIG[this.selectedDenomination];
      this.totalQuantity = (this.batches * coinsPerBatch) + this.singles;
      this.totalValue = this.totalQuantity * this.selectedDenomination;
    }
  }

  isFormValid(): boolean {
    return !!(
      this.selectedSeries &&
      this.selectedDenomination &&
      this.totalQuantity > 0
    );
  }

  getSelectedSeriesLabel(): string {
    return this.selectedSeries ? COIN_SERIES_LABELS[this.selectedSeries] : '';
  }

  getSelectedDenominationLabel(): string {
    return this.selectedDenomination ? COIN_DENOMINATION_LABELS[this.selectedDenomination] : '';
  }

  onAddCoins(): void {
    if (!this.isFormValid()) {
      this.snackBar.open('Please complete all required fields', 'Close', { duration: 3000 });
      return;
    }

    try {
      // Use provided reason or default to "Manual inventory addition"
      const reasonText = this.reason.trim() || 'Manual inventory addition';

      const success = this.inventoryService.addCoins(
        this.selectedSeries!,
        this.selectedDenomination!,
        this.totalQuantity,
        reasonText
      );

      if (success) {
        const quantityDescription = this.batches > 0 && this.singles > 0
          ? `${this.batches} batches + ${this.singles} singles`
          : this.batches > 0
            ? `${this.batches} batches`
            : `${this.singles} singles`;

        this.snackBar.open(
          `Successfully added ${quantityDescription} (${this.totalQuantity} coins) x ${COIN_DENOMINATION_LABELS[this.selectedDenomination!]}`,
          'Close',
          { duration: 4000 }
        );

        this.dialogRef.close({ success: true, added: this.totalQuantity });
      } else {
        this.snackBar.open('Failed to add coins to inventory', 'Close', { duration: 3000 });
      }
    } catch (error) {
      console.error('Error adding coins:', error);
      this.snackBar.open('Error adding coins to inventory', 'Close', { duration: 3000 });
    }
  }

  onCancel(): void {
    this.dialogRef.close({ success: false });
  }

  formatCurrency(amount: number): string {
    if (amount >= 1) {
      return new Intl.NumberFormat('en-ZA', {
        style: 'currency',
        currency: 'ZAR'
      }).format(amount);
    } else {
      return `${Math.round(amount * 100)}c`;
    }
  }

  getCoinsPerBatch(): number {
    return this.selectedDenomination ? COIN_BATCH_CONFIG[this.selectedDenomination] : 0;
  }

  getBatchValue(): number {
    return this.selectedDenomination ? COIN_BATCH_VALUES[this.selectedDenomination] : 0;
  }

  /**
   * Calculate the total number of complete batches that can be formed from the total quantity
   * This includes both the entered batches and any complete batches that can be formed from singles
   */
  getTotalBatches(): number {
    if (!this.selectedDenomination) return 0;
    const coinsPerBatch = COIN_BATCH_CONFIG[this.selectedDenomination];
    return Math.floor(this.totalQuantity / coinsPerBatch);
  }

  /**
   * Calculate the remaining singles after forming complete batches
   */
  getRemainingsingles(): number {
    if (!this.selectedDenomination) return 0;
    const coinsPerBatch = COIN_BATCH_CONFIG[this.selectedDenomination];
    return this.totalQuantity % coinsPerBatch;
  }
}
