import { Component, Inject, OnInit, Renderer2, <PERSON>ement<PERSON>ef, AfterViewInit, OnDestroy } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';

import {
  CoinSeries,
  CoinDenomination,
  COIN_SERIES_LABELS,
  COIN_DENOMINATION_LABELS,
  COIN_BATCH_CONFIG,
  COIN_BATCH_VALUES
} from '../../../../shared/models/inventory.model';
import { InventoryService } from '../../../../shared/services/inventory.service';

export interface RemoveCoinData {
  series?: CoinSeries;
  denomination?: CoinDenomination;
  currentQuantity?: number;
}

export interface RemoveCoinDialogData {
  seriesName?: string;
  denomination?: number;
  currentQuantity?: number;
}

export interface RemoveCoinResult {
  success: boolean;
  removed?: number;
}



@Component({
  selector: 'app-remove-coin-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule
  ],
  templateUrl: './remove-coin-modal.component.html',
  styleUrls: ['./remove-coin-modal.component.scss']
})
export class RemoveCoinModalComponent implements OnInit, AfterViewInit, OnDestroy {
  // Series and denomination options
  availableSeries = Object.values(CoinSeries);
  availableDenominations = [CoinDenomination.R5, CoinDenomination.R2, CoinDenomination.R1, CoinDenomination.C50, CoinDenomination.C20, CoinDenomination.C10];

  // Form state
  selectedSeries: CoinSeries | null = null;
  selectedDenomination: CoinDenomination | null = null;
  batches: number = 0;
  singles: number = 0;
  reason: string = '';

  // Current inventory data
  currentQuantity: number = 0;
  currentBatches: number = 0;
  currentSingles: number = 0;

  // Calculated values
  totalQuantity: number = 0;
  remainingQuantity: number = 0;
  remainingBatches: number = 0;
  remainingSingles: number = 0;
  totalValue: number = 0;

  // Labels for display
  COIN_SERIES_LABELS = COIN_SERIES_LABELS;
  COIN_DENOMINATION_LABELS = COIN_DENOMINATION_LABELS;
  COIN_BATCH_CONFIG = COIN_BATCH_CONFIG;
  COIN_BATCH_VALUES = COIN_BATCH_VALUES;

  constructor(
    public dialogRef: MatDialogRef<RemoveCoinModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: RemoveCoinData,
    private snackBar: MatSnackBar,
    private renderer: Renderer2,
    private elementRef: ElementRef,
    private inventoryService: InventoryService
  ) {
    // Pre-populate from dialog data
    if (data?.series) {
      this.selectedSeries = data.series;
    }
    if (data?.denomination) {
      this.selectedDenomination = data.denomination;
    }
    if (data?.currentQuantity !== undefined) {
      this.currentQuantity = data.currentQuantity;
      this.calculateCurrentInventory();
    }
  }

  ngOnInit(): void {
    // Component initialization
  }

  ngAfterViewInit(): void {
    // Backdrop blur is now handled entirely by CSS for smoother transitions
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  private calculateCurrentInventory(): void {
    if (this.selectedDenomination && this.currentQuantity > 0) {
      const coinsPerBatch = COIN_BATCH_CONFIG[this.selectedDenomination];
      this.currentBatches = Math.floor(this.currentQuantity / coinsPerBatch);
      this.currentSingles = this.currentQuantity % coinsPerBatch;
    } else {
      this.currentBatches = 0;
      this.currentSingles = 0;
    }
  }

  onSeriesChange(): void {
    this.selectedDenomination = null;
    this.resetQuantities();
  }

  onDenominationChange(): void {
    this.calculateCurrentInventory();
    this.resetQuantities();
  }

  private resetQuantities(): void {
    this.batches = 0;
    this.singles = 0;
    this.calculateValues();
  }

  adjustBatches(delta: number): void {
    const maxBatches = this.currentBatches;
    this.batches = Math.max(0, Math.min(maxBatches, this.batches + delta));
    this.onQuantityChange();
  }

  adjustSingles(delta: number): void {
    const maxSingles = this.currentQuantity - (this.batches * this.getCoinsPerBatch());
    this.singles = Math.max(0, Math.min(maxSingles, this.singles + delta));
    this.onQuantityChange();
  }

  onQuantityChange(): void {
    this.calculateValues();
  }

  private calculateValues(): void {
    if (this.selectedDenomination) {
      const coinsPerBatch = COIN_BATCH_CONFIG[this.selectedDenomination];
      this.totalQuantity = (this.batches * coinsPerBatch) + this.singles;
      this.totalValue = this.totalQuantity * this.selectedDenomination;
      this.remainingQuantity = this.currentQuantity - this.totalQuantity;

      // Calculate remaining batches and singles
      this.remainingBatches = Math.floor(this.remainingQuantity / coinsPerBatch);
      this.remainingSingles = this.remainingQuantity % coinsPerBatch;
    }
  }

  isFormValid(): boolean {
    return !!(
      this.selectedSeries &&
      this.selectedDenomination &&
      this.totalQuantity > 0 &&
      this.totalQuantity <= this.currentQuantity
    );
  }

  getSelectedSeriesLabel(): string {
    return this.selectedSeries ? COIN_SERIES_LABELS[this.selectedSeries] : '';
  }

  getSelectedDenominationLabel(): string {
    return this.selectedDenomination ? COIN_DENOMINATION_LABELS[this.selectedDenomination] : '';
  }

  onRemoveCoins(): void {
    if (!this.isFormValid()) {
      this.snackBar.open('Please complete all required fields', 'Close', { duration: 3000 });
      return;
    }

    try {
      // Use provided reason or default to "Manual inventory removal"
      const reasonText = this.reason.trim() || 'Manual inventory removal';

      const success = this.inventoryService.removeCoins(
        this.selectedSeries!,
        this.selectedDenomination!,
        this.totalQuantity,
        reasonText
      );

      if (success) {
        const quantityDescription = this.batches > 0 && this.singles > 0
          ? `${this.batches} batches + ${this.singles} singles`
          : this.batches > 0
            ? `${this.batches} batches`
            : `${this.singles} singles`;

        this.snackBar.open(
          `Successfully removed ${quantityDescription} (${this.totalQuantity} coins) x ${COIN_DENOMINATION_LABELS[this.selectedDenomination!]}`,
          'Close',
          { duration: 4000 }
        );

        this.dialogRef.close({ success: true, removed: this.totalQuantity });
      } else {
        this.snackBar.open('Failed to remove coins from inventory', 'Close', { duration: 3000 });
      }
    } catch (error) {
      console.error('Error removing coins:', error);
      this.snackBar.open('Error removing coins from inventory', 'Close', { duration: 3000 });
    }
  }

  onCancel(): void {
    this.dialogRef.close({ success: false });
  }

  formatCurrency(amount: number): string {
    if (amount >= 1) {
      return new Intl.NumberFormat('en-ZA', {
        style: 'currency',
        currency: 'ZAR'
      }).format(amount);
    } else {
      return `${Math.round(amount * 100)}c`;
    }
  }

  getCoinsPerBatch(): number {
    return this.selectedDenomination ? COIN_BATCH_CONFIG[this.selectedDenomination] : 0;
  }

  getBatchValue(): number {
    return this.selectedDenomination ? COIN_BATCH_VALUES[this.selectedDenomination] : 0;
  }

  getSeriesIcon(series: CoinSeries): string {
    const icons = {
      [CoinSeries.MANDELA]: 'account_balance',
      [CoinSeries.BIG_5]: 'pets',
      [CoinSeries.COMMEMORATIVE]: 'star',
      [CoinSeries.PROTEA]: 'local_florist'
    };
    return icons[series] || 'category';
  }

  getSeriesDescription(series: CoinSeries): string {
    const descriptions = {
      [CoinSeries.MANDELA]: 'Standard circulation coins',
      [CoinSeries.BIG_5]: 'Wildlife themed series',
      [CoinSeries.COMMEMORATIVE]: 'Special edition coins',
      [CoinSeries.PROTEA]: 'Protea themed series'
    };
    return descriptions[series] || 'Coin series';
  }
}
