import { Injectable } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { AddCoinModalComponent, AddCoinDialogData, AddCoinResult } from './add-coin-modal.component';

@Injectable({
  providedIn: 'root'
})
export class AddCoinModalService {

  constructor(private dialog: MatDialog) { }

  /**
   * Opens the Add Coin modal for a specific denomination
   * @param data Configuration data for the modal
   * @returns Observable that emits the result when the modal is closed
   */
  openAddCoinModal(data: AddCoinDialogData): Observable<AddCoinResult | undefined> {
    const dialogRef: MatDialogRef<AddCoinModalComponent> = this.dialog.open(
      AddCoinModalComponent,
      {
        width: 'calc(100vw - 8rem)', // Full width with 4rem margin on each side
        maxWidth: 'calc(100vw - 8rem)',
        maxHeight: '90vh',
        disableClose: false,
        autoFocus: false,
        restoreFocus: true,
        hasBackdrop: true,
        backdropClass: ['add-cash-modal-backdrop', 'blur-backdrop'], // Use same backdrop as add cash
        data: data,
        panelClass: ['add-cash-modal-panel', 'modern-modal-panel'], // Use same panel class as add cash
        enterAnimationDuration: '300ms',
        exitAnimationDuration: '250ms'
      }
    );

    return dialogRef.afterClosed();
  }

  /**
   * Checks if any Add Coin modal is currently open
   * @returns boolean indicating if a modal is open
   */
  isModalOpen(): boolean {
    return this.dialog.openDialogs.some(
      dialog => dialog.componentInstance instanceof AddCoinModalComponent
    );
  }

  /**
   * Closes all open Add Coin modals
   */
  closeAllModals(): void {
    this.dialog.openDialogs
      .filter(dialog => dialog.componentInstance instanceof AddCoinModalComponent)
      .forEach(dialog => dialog.close());
  }
}
