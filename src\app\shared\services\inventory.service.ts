import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { LocalStorageService } from './local-storage.service';
import {
  CashInventory,
  CoinInventory,
  NoteSeries,
  NoteDenomination,
  CoinSeries,
  CoinDenomination,
  InventoryTransaction,
  TransactionType,
  COIN_BATCH_CONFIG,
  COIN_BATCH_VALUES,
  InventorySummary,
  LowStockAlert,
  AlertSeverity,
  DEFAULT_LOW_STOCK_THRESHOLDS,
  CustomNoteSeries,
  NoteSeriesInfo
} from '../models/inventory.model';

@Injectable({
  providedIn: 'root'
})
export class InventoryService {
  private readonly CASH_INVENTORY_KEY = 'cash_inventory';
  private readonly COIN_INVENTORY_KEY = 'coin_inventory';
  private readonly TRANSACTIONS_KEY = 'inventory_transactions';
  private readonly SETTINGS_KEY = 'inventory_settings';
  private readonly CUSTOM_NOTE_SERIES_KEY = 'custom_note_series';

  // Reactive data streams
  private cashInventorySubject = new BehaviorSubject<CashInventory[]>([]);
  private coinInventorySubject = new BehaviorSubject<CoinInventory[]>([]);
  private transactionsSubject = new BehaviorSubject<InventoryTransaction[]>([]);
  private customNoteSeriesSubject = new BehaviorSubject<CustomNoteSeries[]>([]);

  public cashInventory$ = this.cashInventorySubject.asObservable();
  public coinInventory$ = this.coinInventorySubject.asObservable();
  public transactions$ = this.transactionsSubject.asObservable();
  public customNoteSeries$ = this.customNoteSeriesSubject.asObservable();

  constructor(private localStorageService: LocalStorageService) {
    this.initializeInventory();
  }

  /**
   * Initialize inventory data from localStorage or create default data
   */
  private initializeInventory(): void {
    // Load existing data or create default inventory (predefined series get default values, custom series start at 0)
    const cashInventory = this.localStorageService.getItem<CashInventory[]>(this.CASH_INVENTORY_KEY) || this.createDefaultCashInventory();
    const coinInventory = this.localStorageService.getItem<CoinInventory[]>(this.COIN_INVENTORY_KEY) || this.createDefaultCoinInventory();
    const transactions = this.localStorageService.getItem<InventoryTransaction[]>(this.TRANSACTIONS_KEY) || [];
    const customNoteSeries = this.localStorageService.getItem<CustomNoteSeries[]>(this.CUSTOM_NOTE_SERIES_KEY) || [];

    // Update subjects
    this.cashInventorySubject.next(cashInventory);
    this.coinInventorySubject.next(coinInventory);
    this.transactionsSubject.next(transactions);
    this.customNoteSeriesSubject.next(customNoteSeries);

    // Save to localStorage if it was default data
    this.saveCashInventory(cashInventory);
    this.saveCoinInventory(coinInventory);
    this.saveTransactions(transactions);
    this.saveCustomNoteSeries(customNoteSeries);
  }

  /**
   * Create default cash inventory data
   */
  private createDefaultCashInventory(): CashInventory[] {
    const inventory: CashInventory[] = [];
    const series = [NoteSeries.MANDELA, NoteSeries.BIG_5, NoteSeries.COMMEMORATIVE, NoteSeries.V6];
    const denominations = [NoteDenomination.R10, NoteDenomination.R20, NoteDenomination.R50, NoteDenomination.R100, NoteDenomination.R200];

    series.forEach(noteSeries => {
      denominations.forEach(denomination => {
        const id = `cash-${noteSeries}-${denomination}`;
        const quantity = this.getDefaultQuantity(denomination);
        inventory.push({
          id,
          noteSeries,
          denomination,
          quantity,
          value: quantity * denomination,
          lastUpdated: new Date(),
          updatedBy: 'system'
        });
      });
    });

    return inventory;
  }

  /**
   * Create default coin inventory data
   */
  private createDefaultCoinInventory(): CoinInventory[] {
    const inventory: CoinInventory[] = [];
    const series = [CoinSeries.MANDELA, CoinSeries.BIG_5, CoinSeries.COMMEMORATIVE, CoinSeries.PROTEA];
    const denominations = [CoinDenomination.C10, CoinDenomination.C20, CoinDenomination.C50, CoinDenomination.R1, CoinDenomination.R2, CoinDenomination.R5];

    series.forEach(coinSeries => {
      denominations.forEach(denomination => {
        const id = `coin-${coinSeries}-${denomination}`;
        const quantity = this.getDefaultCoinQuantity(denomination);
        const batches = Math.floor(quantity / COIN_BATCH_CONFIG[denomination]);
        inventory.push({
          id,
          series: coinSeries,
          denomination,
          quantity,
          batches,
          value: quantity * denomination,
          lastUpdated: new Date(),
          updatedBy: 'system'
        });
      });
    });

    return inventory;
  }

  /**
   * Get default quantity for cash denominations
   */
  private getDefaultQuantity(denomination: NoteDenomination): number {
    const defaults: { [key in NoteDenomination]: number } = {
      [NoteDenomination.R10]: 1000,
      [NoteDenomination.R20]: 800,
      [NoteDenomination.R50]: 600,
      [NoteDenomination.R100]: 400,
      [NoteDenomination.R200]: 200
    };
    return defaults[denomination];
  }

  /**
   * Get default quantity for coin denominations
   */
  private getDefaultCoinQuantity(denomination: CoinDenomination): number {
    const defaults: { [key in CoinDenomination]: number } = {
      [CoinDenomination.C10]: 2000,
      [CoinDenomination.C20]: 1500,
      [CoinDenomination.C50]: 1000,
      [CoinDenomination.R1]: 800,
      [CoinDenomination.R2]: 600,
      [CoinDenomination.R5]: 400
    };
    return defaults[denomination];
  }

  /**
   * Save cash inventory to localStorage
   */
  private saveCashInventory(inventory: CashInventory[]): void {
    this.localStorageService.setItem(this.CASH_INVENTORY_KEY, inventory);
  }

  /**
   * Save coin inventory to localStorage
   */
  private saveCoinInventory(inventory: CoinInventory[]): void {
    this.localStorageService.setItem(this.COIN_INVENTORY_KEY, inventory);
  }

  /**
   * Save transactions to localStorage
   */
  private saveTransactions(transactions: InventoryTransaction[]): void {
    this.localStorageService.setItem(this.TRANSACTIONS_KEY, transactions);
  }

  /**
   * Get current cash inventory
   */
  getCashInventory(): CashInventory[] {
    return this.cashInventorySubject.value;
  }

  /**
   * Get current coin inventory
   */
  getCoinInventory(): CoinInventory[] {
    return this.coinInventorySubject.value;
  }

  /**
   * Get current transactions
   */
  getTransactions(): InventoryTransaction[] {
    return this.transactionsSubject.value;
  }

  /**
   * Find cash inventory item by series and denomination
   */
  findCashInventory(series: NoteSeries, denomination: NoteDenomination): CashInventory | undefined {
    return this.getCashInventory().find(item =>
      item.noteSeries === series && item.denomination === denomination
    );
  }

  /**
   * Find coin inventory item by series and denomination
   */
  findCoinInventory(series: CoinSeries, denomination: CoinDenomination): CoinInventory | undefined {
    return this.getCoinInventory().find(item =>
      item.series === series && item.denomination === denomination
    );
  }

  /**
   * Get current quantity for cash inventory
   */
  getCurrentCashQuantity(series: NoteSeries, denomination: NoteDenomination): number {
    const item = this.findCashInventory(series, denomination);
    return item ? item.quantity : 0;
  }

  /**
   * Get current quantity for coin inventory
   */
  getCurrentCoinQuantity(series: CoinSeries, denomination: CoinDenomination): number {
    const item = this.findCoinInventory(series, denomination);
    return item ? item.quantity : 0;
  }

  /**
   * Add cash to inventory
   */
  addCash(series: NoteSeries, denomination: NoteDenomination, quantity: number, reason: string, performedBy: string = 'user'): boolean {
    try {
      const inventory = this.getCashInventory();
      let item = this.findCashInventory(series, denomination);

      if (!item) {
        // Create new inventory item if it doesn't exist
        const id = `cash-${series}-${denomination}`;
        item = {
          id,
          noteSeries: series,
          denomination,
          quantity: 0,
          value: 0,
          lastUpdated: new Date(),
          updatedBy: performedBy
        };
        inventory.push(item);
      }

      const previousQuantity = item.quantity;
      const newQuantity = previousQuantity + quantity;
      const newValue = newQuantity * denomination;

      // Update inventory item
      item.quantity = newQuantity;
      item.value = newValue;
      item.lastUpdated = new Date();
      item.updatedBy = performedBy;

      // Create transaction record
      const transaction: InventoryTransaction = {
        id: this.generateTransactionId(),
        type: TransactionType.ADD,
        inventoryId: item.id,
        quantityChange: quantity,
        previousQuantity,
        newQuantity,
        reason,
        performedBy,
        timestamp: new Date()
      };

      // Update data
      this.cashInventorySubject.next([...inventory]);
      this.addTransaction(transaction);
      this.saveCashInventory(inventory);

      return true;
    } catch (error) {
      console.error('Error adding cash:', error);
      return false;
    }
  }

  /**
   * Add cash to custom series inventory
   */
  addCashToCustomSeries(seriesId: string, denomination: NoteDenomination, quantity: number, reason: string, performedBy: string = 'user'): boolean {
    try {
      const inventory = this.getCashInventory();
      const itemId = `cash-${seriesId}-${denomination}`;
      let item = inventory.find(i => i.id === itemId);

      if (!item) {
        // Create new inventory item if it doesn't exist
        item = {
          id: itemId,
          noteSeries: seriesId as any, // Custom series ID
          denomination,
          quantity: 0,
          value: 0,
          lastUpdated: new Date(),
          updatedBy: performedBy
        };
        inventory.push(item);
      }

      const previousQuantity = item.quantity;
      const newQuantity = previousQuantity + quantity;
      const newValue = newQuantity * denomination;

      // Update inventory item
      item.quantity = newQuantity;
      item.value = newValue;
      item.lastUpdated = new Date();
      item.updatedBy = performedBy;

      // Create transaction record
      const transaction: InventoryTransaction = {
        id: this.generateTransactionId(),
        type: TransactionType.ADD,
        inventoryId: item.id,
        quantityChange: quantity,
        previousQuantity,
        newQuantity,
        reason,
        performedBy,
        timestamp: new Date()
      };

      // Update data
      this.cashInventorySubject.next([...inventory]);
      this.addTransaction(transaction);
      this.saveCashInventory(inventory);

      return true;
    } catch (error) {
      console.error('Error adding cash to custom series:', error);
      return false;
    }
  }

  /**
   * Remove cash from inventory
   */
  removeCash(series: NoteSeries, denomination: NoteDenomination, quantity: number, reason: string, performedBy: string = 'user'): boolean {
    try {
      const inventory = this.getCashInventory();
      const item = this.findCashInventory(series, denomination);

      if (!item) {
        console.error('Cash inventory item not found');
        return false;
      }

      if (item.quantity < quantity) {
        console.error('Insufficient quantity in inventory');
        return false;
      }

      const previousQuantity = item.quantity;
      const newQuantity = previousQuantity - quantity;

      // Update inventory item
      item.quantity = newQuantity;
      item.value = newQuantity * denomination;
      item.lastUpdated = new Date();
      item.updatedBy = performedBy;

      // Create transaction record
      const transaction: InventoryTransaction = {
        id: this.generateTransactionId(),
        type: TransactionType.REMOVE,
        inventoryId: item.id,
        quantityChange: -quantity,
        previousQuantity,
        newQuantity,
        reason,
        performedBy,
        timestamp: new Date()
      };

      // Update data
      this.cashInventorySubject.next([...inventory]);
      this.addTransaction(transaction);
      this.saveCashInventory(inventory);

      return true;
    } catch (error) {
      console.error('Error removing cash:', error);
      return false;
    }
  }

  /**
   * Remove cash from custom series inventory
   */
  removeCashFromCustomSeries(seriesId: string, denomination: NoteDenomination, quantity: number, reason: string, performedBy: string = 'user'): boolean {
    try {
      const inventory = this.getCashInventory();
      const itemId = `cash-${seriesId}-${denomination}`;
      const item = inventory.find(i => i.id === itemId);

      if (!item) {
        console.error('Custom series cash inventory item not found');
        return false;
      }

      if (item.quantity < quantity) {
        console.error('Insufficient quantity in custom series inventory');
        return false;
      }

      const previousQuantity = item.quantity;
      const newQuantity = previousQuantity - quantity;

      // Update inventory item
      item.quantity = newQuantity;
      item.value = newQuantity * denomination;
      item.lastUpdated = new Date();
      item.updatedBy = performedBy;

      // Create transaction record
      const transaction: InventoryTransaction = {
        id: this.generateTransactionId(),
        type: TransactionType.REMOVE,
        inventoryId: item.id,
        quantityChange: -quantity,
        previousQuantity,
        newQuantity,
        reason,
        performedBy,
        timestamp: new Date()
      };

      // Update data
      this.cashInventorySubject.next([...inventory]);
      this.addTransaction(transaction);
      this.saveCashInventory(inventory);

      return true;
    } catch (error) {
      console.error('Error removing cash from custom series:', error);
      return false;
    }
  }

  /**
   * Add coins to inventory
   */
  addCoins(series: CoinSeries, denomination: CoinDenomination, quantity: number, reason: string, performedBy: string = 'user'): boolean {
    try {
      const inventory = this.getCoinInventory();
      let item = this.findCoinInventory(series, denomination);

      if (!item) {
        // Create new inventory item if it doesn't exist
        const id = `coin-${series}-${denomination}`;
        item = {
          id,
          series,
          denomination,
          quantity: 0,
          batches: 0,
          value: 0,
          lastUpdated: new Date(),
          updatedBy: performedBy
        };
        inventory.push(item);
      }

      const previousQuantity = item.quantity;
      const newQuantity = previousQuantity + quantity;
      const newBatches = Math.floor(newQuantity / COIN_BATCH_CONFIG[denomination]);

      // Update inventory item
      item.quantity = newQuantity;
      item.batches = newBatches;
      item.value = newQuantity * denomination;
      item.lastUpdated = new Date();
      item.updatedBy = performedBy;

      // Create transaction record
      const transaction: InventoryTransaction = {
        id: this.generateTransactionId(),
        type: TransactionType.ADD,
        inventoryId: item.id,
        quantityChange: quantity,
        previousQuantity,
        newQuantity,
        reason,
        performedBy,
        timestamp: new Date()
      };

      // Update data
      this.coinInventorySubject.next([...inventory]);
      this.addTransaction(transaction);
      this.saveCoinInventory(inventory);

      return true;
    } catch (error) {
      console.error('Error adding coins:', error);
      return false;
    }
  }

  /**
   * Remove coins from inventory
   */
  removeCoins(series: CoinSeries, denomination: CoinDenomination, quantity: number, reason: string, performedBy: string = 'user'): boolean {
    try {
      const inventory = this.getCoinInventory();
      const item = this.findCoinInventory(series, denomination);

      if (!item) {
        console.error('Coin inventory item not found');
        return false;
      }

      if (item.quantity < quantity) {
        console.error('Insufficient quantity in inventory');
        return false;
      }

      const previousQuantity = item.quantity;
      const newQuantity = previousQuantity - quantity;
      const newBatches = Math.floor(newQuantity / COIN_BATCH_CONFIG[denomination]);

      // Update inventory item
      item.quantity = newQuantity;
      item.batches = newBatches;
      item.value = newQuantity * denomination;
      item.lastUpdated = new Date();
      item.updatedBy = performedBy;

      // Create transaction record
      const transaction: InventoryTransaction = {
        id: this.generateTransactionId(),
        type: TransactionType.REMOVE,
        inventoryId: item.id,
        quantityChange: -quantity,
        previousQuantity,
        newQuantity,
        reason,
        performedBy,
        timestamp: new Date()
      };

      // Update data
      this.coinInventorySubject.next([...inventory]);
      this.addTransaction(transaction);
      this.saveCoinInventory(inventory);

      return true;
    } catch (error) {
      console.error('Error removing coins:', error);
      return false;
    }
  }

  /**
   * Add transaction to the list
   */
  private addTransaction(transaction: InventoryTransaction): void {
    const transactions = this.getTransactions();
    transactions.push(transaction);
    this.transactionsSubject.next([...transactions]);
    this.saveTransactions(transactions);
  }

  /**
   * Generate unique transaction ID
   */
  private generateTransactionId(): string {
    return `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get inventory summary with totals and alerts
   */
  getInventorySummary(): InventorySummary {
    const cashInventory = this.getCashInventory();
    const coinInventory = this.getCoinInventory();

    let totalValue = 0;
    let totalNotes = 0;
    const lowStockAlerts: LowStockAlert[] = [];

    // Calculate cash totals and check for low stock
    cashInventory.forEach(item => {
      totalValue += item.value;
      totalNotes += item.quantity;

      const threshold = DEFAULT_LOW_STOCK_THRESHOLDS[item.denomination.toString()] || 0;
      if (item.quantity <= threshold) {
        lowStockAlerts.push({
          inventoryId: item.id,
          series: item.noteSeries,
          denomination: item.denomination,
          currentQuantity: item.quantity,
          minimumThreshold: threshold,
          severity: this.getAlertSeverity(item.quantity, threshold)
        });
      }
    });

    // Add coin values to total
    coinInventory.forEach(item => {
      totalValue += item.value;
    });

    return {
      totalValue,
      totalNotes,
      seriesBreakdown: [],
      denominationBreakdown: [],
      lowStockAlerts
    };
  }

  /**
   * Get alert severity based on current quantity vs threshold
   */
  private getAlertSeverity(currentQuantity: number, threshold: number): AlertSeverity {
    if (currentQuantity === 0) return AlertSeverity.CRITICAL;
    if (currentQuantity <= threshold * 0.25) return AlertSeverity.HIGH;
    if (currentQuantity <= threshold * 0.5) return AlertSeverity.MEDIUM;
    return AlertSeverity.LOW;
  }

  /**
   * Reset all inventory data (for testing/demo purposes)
   */
  resetInventory(): void {
    const cashInventory = this.createDefaultCashInventory();
    const coinInventory = this.createDefaultCoinInventory();
    const transactions: InventoryTransaction[] = [];

    this.cashInventorySubject.next(cashInventory);
    this.coinInventorySubject.next(coinInventory);
    this.transactionsSubject.next(transactions);

    this.saveCashInventory(cashInventory);
    this.saveCoinInventory(coinInventory);
    this.saveTransactions(transactions);
  }

  /**
   * Reset all inventory to zero quantities (for fresh start)
   */
  resetInventoryToZero(): void {
    const cashInventory = this.createZeroCashInventory();
    const coinInventory = this.createZeroCoinInventory();
    const transactions: InventoryTransaction[] = [];

    this.cashInventorySubject.next(cashInventory);
    this.coinInventorySubject.next(coinInventory);
    this.transactionsSubject.next(transactions);

    this.saveCashInventory(cashInventory);
    this.saveCoinInventory(coinInventory);
    this.saveTransactions(transactions);
  }

  /**
   * Create zero cash inventory data
   */
  private createZeroCashInventory(): CashInventory[] {
    const inventory: CashInventory[] = [];
    const series = [NoteSeries.MANDELA, NoteSeries.BIG_5, NoteSeries.COMMEMORATIVE, NoteSeries.V6];
    const denominations = [NoteDenomination.R10, NoteDenomination.R20, NoteDenomination.R50, NoteDenomination.R100, NoteDenomination.R200];

    series.forEach(noteSeries => {
      denominations.forEach(denomination => {
        const id = `cash-${noteSeries}-${denomination}`;
        inventory.push({
          id,
          noteSeries,
          denomination,
          quantity: 0,
          value: 0,
          lastUpdated: new Date(),
          updatedBy: 'system'
        });
      });
    });

    return inventory;
  }

  /**
   * Create zero coin inventory data
   */
  private createZeroCoinInventory(): CoinInventory[] {
    const inventory: CoinInventory[] = [];
    const series = [CoinSeries.MANDELA, CoinSeries.BIG_5, CoinSeries.COMMEMORATIVE, CoinSeries.PROTEA];
    const denominations = [CoinDenomination.C10, CoinDenomination.C20, CoinDenomination.C50, CoinDenomination.R1, CoinDenomination.R2, CoinDenomination.R5];

    series.forEach(coinSeries => {
      denominations.forEach(denomination => {
        const id = `coin-${coinSeries}-${denomination}`;
        inventory.push({
          id,
          series: coinSeries,
          denomination,
          quantity: 0,
          batches: 0,
          value: 0,
          lastUpdated: new Date(),
          updatedBy: 'system'
        });
      });
    });

    return inventory;
  }

  /**
   * Get transactions for a specific inventory item
   */
  getTransactionsForItem(inventoryId: string): InventoryTransaction[] {
    return this.getTransactions().filter(transaction => transaction.inventoryId === inventoryId);
  }

  /**
   * Get recent transactions (last N transactions)
   */
  getRecentTransactions(limit: number = 10): InventoryTransaction[] {
    return this.getTransactions()
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  /**
   * Export inventory data for backup
   */
  exportInventoryData(): any {
    return {
      cashInventory: this.getCashInventory(),
      coinInventory: this.getCoinInventory(),
      transactions: this.getTransactions(),
      exportDate: new Date().toISOString()
    };
  }

  /**
   * Import inventory data from backup
   */
  importInventoryData(data: any): boolean {
    try {
      if (data.cashInventory) {
        this.cashInventorySubject.next(data.cashInventory);
        this.saveCashInventory(data.cashInventory);
      }
      if (data.coinInventory) {
        this.coinInventorySubject.next(data.coinInventory);
        this.saveCoinInventory(data.coinInventory);
      }
      if (data.transactions) {
        this.transactionsSubject.next(data.transactions);
        this.saveTransactions(data.transactions);
      }
      return true;
    } catch (error) {
      console.error('Error importing inventory data:', error);
      return false;
    }
  }

  // ===== CUSTOM NOTE SERIES MANAGEMENT =====

  /**
   * Get all custom note series
   */
  getCustomNoteSeries(): CustomNoteSeries[] {
    return this.customNoteSeriesSubject.value;
  }

  /**
   * Get all note series (predefined + custom)
   */
  getAllNoteSeries(): NoteSeriesInfo[] {
    const predefinedSeries: NoteSeriesInfo[] = Object.values(NoteSeries).map(series => ({
      id: series,
      name: this.getPredefinedSeriesName(series),
      description: this.getPredefinedSeriesDescription(series),
      denominations: Object.values(NoteDenomination).filter(d => typeof d === 'number') as NoteDenomination[],
      isActive: this.hasInventoryForSeries(series),
      isPredefined: true,
      icon: this.getPredefinedSeriesIcon(series)
    }));

    const customSeries: NoteSeriesInfo[] = this.getCustomNoteSeries().map(series => ({
      id: series.id,
      name: series.name,
      description: series.description,
      denominations: series.denominations,
      isActive: series.isActive,
      isPredefined: false
    }));

    return [...predefinedSeries, ...customSeries];
  }

  /**
   * Add a new custom note series
   */
  addCustomNoteSeries(name: string, description: string, denominations: NoteDenomination[], createdBy: string = 'user'): boolean {
    try {
      const customSeries = this.getCustomNoteSeries();

      // Check if series name already exists
      const existingPredefined = Object.values(NoteSeries).some(series =>
        this.getPredefinedSeriesName(series).toLowerCase() === name.toLowerCase()
      );
      const existingCustom = customSeries.some(series =>
        series.name.toLowerCase() === name.toLowerCase()
      );

      if (existingPredefined || existingCustom) {
        console.error('Series name already exists');
        return false;
      }

      const newSeries: CustomNoteSeries = {
        id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: name.trim(),
        description: description.trim(),
        denominations: [...denominations],
        isActive: true,
        createdDate: new Date(),
        createdBy
      };

      const updatedSeries = [...customSeries, newSeries];
      this.customNoteSeriesSubject.next(updatedSeries);
      this.saveCustomNoteSeries(updatedSeries);

      // Create initial inventory entries for the new series
      this.createInventoryForCustomSeries(newSeries);

      return true;
    } catch (error) {
      console.error('Error adding custom note series:', error);
      return false;
    }
  }

  /**
   * Remove a custom note series
   */
  removeCustomNoteSeries(seriesId: string): boolean {
    try {
      const customSeries = this.getCustomNoteSeries();
      const seriesToRemove = customSeries.find(s => s.id === seriesId);

      if (!seriesToRemove) {
        console.error('Custom series not found');
        return false;
      }

      // Check if series has inventory
      if (this.hasInventoryForCustomSeries(seriesId)) {
        console.error('Cannot remove series with existing inventory');
        return false;
      }

      const updatedSeries = customSeries.filter(s => s.id !== seriesId);
      this.customNoteSeriesSubject.next(updatedSeries);
      this.saveCustomNoteSeries(updatedSeries);

      // Remove any inventory entries for this series
      this.removeInventoryForCustomSeries(seriesId);

      return true;
    } catch (error) {
      console.error('Error removing custom note series:', error);
      return false;
    }
  }

  /**
   * Save custom note series to localStorage
   */
  private saveCustomNoteSeries(customSeries: CustomNoteSeries[]): void {
    this.localStorageService.setItem(this.CUSTOM_NOTE_SERIES_KEY, customSeries);
  }

  /**
   * Get predefined series name
   */
  private getPredefinedSeriesName(series: NoteSeries): string {
    const labels = {
      [NoteSeries.MANDELA]: 'Mandela Series',
      [NoteSeries.BIG_5]: 'Big 5 Series',
      [NoteSeries.COMMEMORATIVE]: 'Commemorative Series',
      [NoteSeries.V6]: 'V6 Series'
    };
    return labels[series] || series;
  }

  /**
   * Get predefined series description
   */
  private getPredefinedSeriesDescription(series: NoteSeries): string {
    const descriptions = {
      [NoteSeries.MANDELA]: 'Standard circulation notes featuring Nelson Mandela',
      [NoteSeries.BIG_5]: 'Wildlife themed series featuring the Big 5 animals',
      [NoteSeries.COMMEMORATIVE]: 'Special edition commemorative notes',
      [NoteSeries.V6]: 'Latest series design with enhanced security features'
    };
    return descriptions[series] || 'Predefined note series';
  }

  /**
   * Get predefined series icon
   */
  private getPredefinedSeriesIcon(series: NoteSeries): string {
    const icons = {
      [NoteSeries.MANDELA]: 'account_balance',
      [NoteSeries.BIG_5]: 'nature',
      [NoteSeries.COMMEMORATIVE]: 'star',
      [NoteSeries.V6]: 'new_releases'
    };
    return icons[series] || 'category';
  }

  /**
   * Check if series has inventory
   */
  private hasInventoryForSeries(series: NoteSeries): boolean {
    const cashInventory = this.getCashInventory();
    return cashInventory.some(item => item.noteSeries === series && item.quantity > 0);
  }

  /**
   * Check if custom series has inventory
   */
  private hasInventoryForCustomSeries(seriesId: string): boolean {
    const cashInventory = this.getCashInventory();
    return cashInventory.some(item => item.id.includes(seriesId) && item.quantity > 0);
  }

  /**
   * Create initial inventory entries for custom series
   */
  private createInventoryForCustomSeries(series: CustomNoteSeries): void {
    const cashInventory = this.getCashInventory();

    series.denominations.forEach(denomination => {
      const id = `cash-${series.id}-${denomination}`;
      const inventoryItem: CashInventory = {
        id,
        noteSeries: series.id as any, // We'll need to handle this differently
        denomination,
        quantity: 0,
        value: 0,
        lastUpdated: new Date(),
        updatedBy: 'system'
      };

      cashInventory.push(inventoryItem);
    });

    this.cashInventorySubject.next(cashInventory);
    this.saveCashInventory(cashInventory);
  }

  /**
   * Remove inventory entries for custom series
   */
  private removeInventoryForCustomSeries(seriesId: string): void {
    const cashInventory = this.getCashInventory();
    const filteredInventory = cashInventory.filter(item => !item.id.includes(seriesId));

    this.cashInventorySubject.next(filteredInventory);
    this.saveCashInventory(filteredInventory);
  }
}
