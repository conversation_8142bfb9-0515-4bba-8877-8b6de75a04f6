<div class="add-coin-modal-container">
  <!-- Modern Header with Gradient -->
  <div class="modal-header">
    <div class="header-content">
      <div class="header-icon">
        <mat-icon>monetization_on</mat-icon>
      </div>
      <div class="header-text">
        <h2>Add Coin Inventory</h2>
        <p>Enhance your coin reserves with precision</p>
      </div>
    </div>
    <button mat-icon-button class="close-button" (click)="onCancel()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Main Content Area -->
  <div class="modal-content">
    <form #addCoinForm="ngForm" class="add-coin-form">

      <!-- Step 1: Series Selection -->
      <div class="form-section series-selection">
        <div class="section-header">
          <mat-icon class="step-icon">category</mat-icon>
          <h3>Select Coin Series</h3>
        </div>
        <div class="series-grid">
          <div *ngFor="let series of availableSeries"
               class="series-card"
               [class.selected]="selectedSeries === series"
               (click)="selectedSeries = series; onSeriesChange()">
            <div class="series-icon">
              <mat-icon>{{ series === availableSeries[0] ? 'account_balance' : series === availableSeries[1] ? 'pets' : series === availableSeries[2] ? 'star' : 'local_florist' }}</mat-icon>
            </div>
            <div class="series-info">
              <h4>{{ COIN_SERIES_LABELS[series] }}</h4>
              <p>Premium coin design</p>
            </div>
            <div class="selection-indicator" *ngIf="selectedSeries === series">
              <mat-icon>check_circle</mat-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 2: Denomination Selection -->
      <div class="form-section denomination-section" *ngIf="selectedSeries">
        <div class="section-header">
          <mat-icon class="step-icon">payments</mat-icon>
          <h3>Choose Denomination</h3>
          <span class="pre-selected-badge" *ngIf="selectedDenomination && data?.denomination">
            <mat-icon>check_circle</mat-icon>
            Pre-selected
          </span>
        </div>
        <div class="denomination-grid">
          <div class="denomination-card"
               *ngFor="let denomination of availableDenominations"
               [class.selected]="selectedDenomination === denomination"
               (click)="selectedDenomination = denomination; onDenominationChange()">
            <div class="denomination-icon">
              <mat-icon>{{ +denomination >= 1 ? 'account_balance_wallet' : 'monetization_on' }}</mat-icon>
            </div>
            <div class="denomination-info">
              <h4>{{ COIN_DENOMINATION_LABELS[denomination] }}</h4>
              <p>{{ +denomination >= 1 ? 'High value coin' : 'Standard coin' }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 3: Quantity Input -->
      <div class="form-section quantity-section" *ngIf="selectedDenomination">
        <div class="section-header">
          <mat-icon class="step-icon">calculate</mat-icon>
          <h3>Specify Quantity</h3>
        </div>
        <div class="quantity-controls">
          <div class="quantity-card batches-card">
            <div class="card-header">
              <mat-icon>inventory_2</mat-icon>
              <h4>Batches</h4>
              <span class="helper-text">{{ selectedDenomination ? COIN_BATCH_CONFIG[selectedDenomination] : 0 }} coins each</span>
            </div>
            <div class="input-container">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustBatches(-1)" [disabled]="batches <= 0">
                <mat-icon>remove</mat-icon>
              </button>
              <input type="number"
                     [(ngModel)]="batches"
                     name="batches"
                     min="0"
                     class="quantity-input"
                     (input)="onQuantityChange()">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustBatches(1)">
                <mat-icon>add</mat-icon>
              </button>
            </div>
            <div class="quantity-info">
              <span class="batch-value">Value per batch: {{ selectedDenomination ? formatCurrency(COIN_BATCH_VALUES[selectedDenomination]) : formatCurrency(0) }}</span>
            </div>
          </div>

          <div class="quantity-card singles-card">
            <div class="card-header">
              <mat-icon>looks_one</mat-icon>
              <h4>Singles</h4>
              <span class="helper-text">Individual coins</span>
            </div>
            <div class="input-container">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustSingles(-1)" [disabled]="singles <= 0">
                <mat-icon>remove</mat-icon>
              </button>
              <input type="number"
                     [(ngModel)]="singles"
                     name="singles"
                     min="0"
                     class="quantity-input"
                     (input)="onQuantityChange()">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustSingles(1)">
                <mat-icon>add</mat-icon>
              </button>
            </div>
            <div class="quantity-info">
              <span class="coin-value">Value per coin: {{ formatCurrency(selectedDenomination) }}</span>
            </div>
          </div>
        </div>

        <!-- Summary Section -->
        <div class="summary-section" *ngIf="totalQuantity > 0">
          <div class="summary-card">
            <div class="summary-header">
              <mat-icon>summarize</mat-icon>
              <h4>Addition Summary</h4>
            </div>
            <div class="summary-content">
              <div class="summary-row">
                <span class="summary-label">Total Coins:</span>
                <span class="summary-value">{{ totalQuantity }} coins</span>
              </div>
              <div class="summary-row">
                <span class="summary-label">Total Value:</span>
                <span class="summary-value">{{ formatCurrency(totalValue) }}</span>
              </div>
            </div>

            <div class="breakdown-section" *ngIf="batches > 0 || singles > 0">
              <div class="breakdown-title">
                <mat-icon>analytics</mat-icon>
                <span>Breakdown</span>
              </div>
              <div class="breakdown-items">
                <div class="breakdown-item" *ngIf="batches > 0">
                  <span class="breakdown-label">{{ batches }} batch{{ batches !== 1 ? 'es' : '' }}</span>
                  <span class="breakdown-value">{{ batches * (selectedDenomination ? COIN_BATCH_CONFIG[selectedDenomination] : 0) }} coins</span>
                </div>
                <div class="breakdown-item" *ngIf="singles > 0">
                  <span class="breakdown-label">{{ singles }} single{{ singles !== 1 ? 's' : '' }}</span>
                  <span class="breakdown-value">{{ singles }} coins</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 4: Reason (Optional) -->
      <div class="form-section reason-section" *ngIf="selectedDenomination">
        <div class="section-header">
          <mat-icon class="step-icon">description</mat-icon>
          <h3>Reason (Optional)</h3>
        </div>
        <mat-form-field class="reason-field" appearance="outline">
          <mat-label>Reason for addition</mat-label>
          <input matInput
                 [(ngModel)]="reason"
                 name="reason"
                 placeholder="e.g., New coin delivery, Inventory replenishment"
                 maxlength="200">
          <mat-hint>Optional: Provide a reason for this addition</mat-hint>
        </mat-form-field>
      </div>
    </form>
  </div>

  <!-- Action Buttons -->
  <div class="modal-actions">
    <button mat-stroked-button class="cancel-btn" (click)="onCancel()">
      <mat-icon>cancel</mat-icon>
      Cancel
    </button>
    <button mat-raised-button
            class="add-btn"
            [disabled]="!isFormValid()"
            (click)="onAddCoins()">
      <mat-icon>add_circle</mat-icon>
      Add to Inventory
    </button>
  </div>
</div>
