import { Injectable } from '@angular/core';
import * as ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';

import { AuditReportSummary } from '../../features/qa-manager/components/audit-report-modal/audit-report-modal.component';

@Injectable({
  providedIn: 'root'
})
export class ExcelExportService {

  constructor() { }

  /**
   * Export audit report to Excel with professional tables and advanced styling
   */
  async exportAuditReportToExcel(auditReport: AuditReportSummary): Promise<void> {
    try {
      // Create a new workbook with ExcelJS
      const workbook = new ExcelJS.Workbook();

      // Set workbook properties
      workbook.creator = 'FFA Currency Management System';
      workbook.lastModifiedBy = 'FFA CMS';
      workbook.created = new Date();
      workbook.modified = new Date();
      workbook.lastPrinted = new Date();

      // 1. Executive Summary Sheet
      await this.addExecutiveSummarySheet(workbook, auditReport);

      // 2. Note Series Details Sheet
      await this.addNoteSeriesDetailsSheet(workbook, auditReport);

      // 3. Coin Portfolio Analysis Sheet
      await this.addCoinPortfolioSheet(workbook, auditReport);

      // 4. Low Stock Alerts Sheet
      await this.addLowStockAlertsSheet(workbook, auditReport);

      // 5. Denomination Breakdown Sheet
      await this.addDenominationBreakdownSheet(workbook, auditReport);

      // 6. Detailed Inventory Master List (Main Table)
      await this.addDetailedInventorySheet(workbook, auditReport);

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
      const filename = `FFA_Inventory_Audit_Report_${timestamp}.xlsx`;

      // Write and save the file
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      saveAs(blob, filename);

      console.log('Professional Excel report exported successfully:', filename);
    } catch (error) {
      console.error('Error exporting Excel report:', error);
      throw new Error('Failed to export Excel report');
    }
  }

  private async addExecutiveSummarySheet(workbook: ExcelJS.Workbook, auditReport: AuditReportSummary): Promise<void> {
    const worksheet = workbook.addWorksheet('📊 Executive Summary', {
      properties: { tabColor: { argb: 'FF1F4E79' } }
    });

    // Set column widths
    worksheet.columns = [
      { width: 35 }, // A - Labels
      { width: 20 }, // B - Values
      { width: 15 }, // C - Status/Percentage
      { width: 15 }, // D - Performance
      { width: 15 }, // E - Additional
      { width: 15 }  // F - Additional
    ];

    // Title Section
    const titleRow = worksheet.addRow(['📊 FFA INVENTORY AUDIT - EXECUTIVE SUMMARY']);
    titleRow.height = 35;
    worksheet.mergeCells('A1:F1');
    titleRow.getCell(1).style = {
      font: { name: 'Calibri', size: 20, bold: true, color: { argb: 'FFFFFFFF' } },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF1F4E79' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
      border: this.getAllBorders()
    };

    // Date row
    const dateRow = worksheet.addRow([`Generated: ${auditReport.generatedDate.toLocaleString()}`]);
    worksheet.mergeCells('A2:F2');
    dateRow.getCell(1).style = {
      font: { name: 'Calibri', size: 12, italic: true, color: { argb: 'FF666666' } },
      alignment: { horizontal: 'center' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } }
    };

    // Empty row
    worksheet.addRow([]);

    // Key Performance Indicators Section
    const kpiHeaderRow = worksheet.addRow(['🎯 KEY PERFORMANCE INDICATORS']);
    worksheet.mergeCells('A4:F4');
    kpiHeaderRow.getCell(1).style = {
      font: { name: 'Calibri', size: 16, bold: true, color: { argb: 'FF1F4E79' } },
      alignment: { horizontal: 'left', vertical: 'middle' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE7F3FF' } },
      border: this.getAllBorders()
    };

    worksheet.addRow([]); // Empty row

    // KPI Table
    const totalValue = auditReport.totalInventoryValue + auditReport.totalCoinValue;
    const kpiData = [
      ['💰 Total Portfolio Value', this.formatCurrency(totalValue), '100%', '🎯 TARGET'],
      ['💵 Notes Portfolio Value', this.formatCurrency(auditReport.totalInventoryValue), `${((auditReport.totalInventoryValue / totalValue) * 100).toFixed(1)}%`, '📈 STRONG'],
      ['🪙 Coins Portfolio Value', this.formatCurrency(auditReport.totalCoinValue), `${((auditReport.totalCoinValue / totalValue) * 100).toFixed(1)}%`, '⚡ STABLE'],
      ['📄 Total Notes Count', this.formatNumber(auditReport.totalNotes), 'ITEMS', '📊 TRACKED'],
      ['⚪ Total Coins Count', this.formatNumber(auditReport.totalCoins), 'ITEMS', '📊 TRACKED'],
      ['⚠️ Low Stock Alerts', auditReport.lowStockItems.length.toString(), 'ALERTS', auditReport.lowStockItems.length === 0 ? '✅ EXCELLENT' : '⚠️ ATTENTION']
    ];

    // Add KPI table with professional styling
    kpiData.forEach((rowData, index) => {
      const row = worksheet.addRow(rowData);
      row.height = 25;
      row.eachCell((cell, colNumber) => {
        if (colNumber <= 4) {
          cell.style = {
            font: { name: 'Calibri', size: 11, bold: colNumber === 2 },
            alignment: { horizontal: colNumber === 1 ? 'left' : 'center', vertical: 'middle' },
            border: this.getAllBorders(),
            fill: {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: index % 2 === 0 ? 'FFFFFFFF' : 'FFF8F9FA' }
            }
          };

          if (colNumber === 4) { // Status column
            cell.style.font!.bold = true;
            cell.style.font!.color = { argb: this.getStatusColor(rowData[3] as string) };
          }
        }
      });
    });

    // Add spacing after KPI section
    worksheet.addRow([]);
    worksheet.addRow([]);

    // Note Series Performance Section
    const noteHeaderRow = worksheet.addRow(['💵 NOTE SERIES PERFORMANCE BREAKDOWN']);
    worksheet.mergeCells(`A${noteHeaderRow.number}:F${noteHeaderRow.number}`);
    noteHeaderRow.getCell(1).style = {
      font: { name: 'Calibri', size: 16, bold: true, color: { argb: 'FF1F4E79' } },
      alignment: { horizontal: 'left', vertical: 'middle' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE7F3FF' } },
      border: this.getAllBorders()
    };

    worksheet.addRow([]); // Empty row

    // Note Series Table Headers
    const noteTableHeaders = ['Series Name', 'Notes Count', 'Batches', 'Singles', 'Portfolio Value', 'Performance'];
    const noteHeadersRow = worksheet.addRow(noteTableHeaders);
    noteHeadersRow.height = 25;
    noteHeadersRow.eachCell((cell, colNumber) => {
      cell.style = {
        font: { name: 'Calibri', size: 12, bold: true, color: { argb: 'FFFFFFFF' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: this.getAllBorders()
      };
    });

    // Note Series Data
    auditReport.noteSeriesSummary.forEach((series, index) => {
      const performance = this.calculatePerformanceIndicator(series.totalValue, totalValue);
      const row = worksheet.addRow([
        `📋 ${series.seriesName}`,
        this.formatNumber(series.totalNotes),
        this.formatNumber(series.totalBatches),
        this.formatNumber(series.totalSingles),
        this.formatCurrency(series.totalValue),
        performance
      ]);

      row.height = 22;
      row.eachCell((cell, colNumber) => {
        cell.style = {
          font: { name: 'Calibri', size: 11, bold: colNumber === 5 },
          alignment: { horizontal: colNumber === 1 ? 'left' : 'center', vertical: 'middle' },
          border: this.getAllBorders(),
          fill: {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: index % 2 === 0 ? 'FFFFFFFF' : 'FFF8F9FA' }
          }
        };

        if (colNumber === 6) { // Performance column
          cell.style.font!.bold = true;
          cell.style.font!.color = { argb: this.getStatusColor(performance) };
        }
      });
    });

    // Create Excel Table for Note Series
    const noteTableStartRow = noteHeadersRow.number;
    const noteTableEndRow = noteTableStartRow + auditReport.noteSeriesSummary.length;
    const noteTableRange = `A${noteTableStartRow}:F${noteTableEndRow}`;

    worksheet.addTable({
      name: 'NoteSeriesTable',
      ref: noteTableRange,
      headerRow: true,
      style: {
        theme: 'TableStyleMedium2',
        showRowStripes: true
      },
      columns: noteTableHeaders.map(header => ({ name: header })),
      rows: auditReport.noteSeriesSummary.map(series => [
        `📋 ${series.seriesName}`,
        this.formatNumber(series.totalNotes),
        this.formatNumber(series.totalBatches),
        this.formatNumber(series.totalSingles),
        this.formatCurrency(series.totalValue),
        this.calculatePerformanceIndicator(series.totalValue, totalValue)
      ])
    });
  }

  private formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(value);
  }

  private formatNumber(value: number): string {
    return new Intl.NumberFormat('en-ZA').format(value);
  }

  // ===== EXCELJS HELPER METHODS =====

  /**
   * Get all borders for ExcelJS cells
   */
  private getAllBorders(): any {
    return {
      top: { style: 'thin', color: { argb: 'FF666666' } },
      left: { style: 'thin', color: { argb: 'FF666666' } },
      bottom: { style: 'thin', color: { argb: 'FF666666' } },
      right: { style: 'thin', color: { argb: 'FF666666' } }
    };
  }

  /**
   * Get status color based on status text
   */
  private getStatusColor(status: string): string {
    if (status.includes('EXCELLENT') || status.includes('✅')) return 'FF0F5132';
    if (status.includes('ATTENTION') || status.includes('⚠️')) return 'FF664D03';
    if (status.includes('CRITICAL') || status.includes('🚨')) return 'FF842029';
    if (status.includes('STRONG') || status.includes('📈')) return 'FF0A58CA';
    if (status.includes('STABLE') || status.includes('⚡')) return 'FF6F42C1';
    return 'FF1F4E79'; // Default blue
  }

  /**
   * Calculate performance indicator for series
   */
  private calculatePerformanceIndicator(seriesValue: number, totalValue: number): string {
    const percentage = (seriesValue / totalValue) * 100;
    if (percentage >= 30) return '🔥 DOMINANT';
    if (percentage >= 20) return '📈 STRONG';
    if (percentage >= 10) return '⚡ ACTIVE';
    if (percentage >= 5) return '📊 MODERATE';
    return '🔍 MINIMAL';
  }

  private async addNoteSeriesDetailsSheet(workbook: ExcelJS.Workbook, auditReport: AuditReportSummary): Promise<void> {
    const worksheet = workbook.addWorksheet('📋 Note Series Details', {
      properties: { tabColor: { argb: 'FF2E7D32' } }
    });

    // Set column widths
    worksheet.columns = [
      { width: 25 }, // A - Series Name
      { width: 18 }, // B - Denomination
      { width: 15 }, // C - Quantity
      { width: 12 }, // D - Batches
      { width: 12 }, // E - Singles
      { width: 18 }, // F - Value
      { width: 15 }  // G - Stock Level
    ];

    // Title row
    const titleRow = worksheet.addRow(['📋 NOTE SERIES DETAILED BREAKDOWN']);
    worksheet.mergeCells('A1:G1');
    titleRow.getCell(1).style = {
      font: { name: 'Calibri', size: 16, bold: true, color: { argb: 'FFFFFFFF' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF2E7D32' } }
    };
    titleRow.height = 30;

    // Date row
    const dateRow = worksheet.addRow([`Generated: ${auditReport.generatedDate.toLocaleString()}`]);
    worksheet.mergeCells('A2:G2');
    dateRow.getCell(1).style = {
      font: { name: 'Calibri', size: 12, italic: true, color: { argb: 'FF666666' } },
      alignment: { horizontal: 'center' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } }
    };

    // Empty row
    worksheet.addRow([]);

    // Prepare data for all note series
    const noteSeriesData: any[][] = [];
    auditReport.noteSeriesSummary.forEach(series => {
      series.denominations.forEach(denom => {
        noteSeriesData.push([
          series.seriesName,
          denom.denominationLabel,
          denom.quantity,
          denom.batches,
          denom.singles,
          denom.value,
          denom.stockLevel.toUpperCase()
        ]);
      });
    });

    // Add headers
    const headers = ['Series Name', 'Denomination', 'Quantity', 'Batches', 'Singles', 'Value', 'Stock Level'];
    const headerRow = worksheet.addRow(headers);
    headerRow.height = 25;
    headerRow.eachCell((cell) => {
      cell.style = {
        font: { name: 'Calibri', size: 12, bold: true, color: { argb: 'FFFFFFFF' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF2E7D32' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: {
          top: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          bottom: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
        }
      };
    });

    // Add data rows
    noteSeriesData.forEach(rowData => {
      const row = worksheet.addRow(rowData);
      row.eachCell((cell, colNumber) => {
        cell.style = {
          font: { name: 'Calibri', size: 11 },
          alignment: { horizontal: colNumber >= 3 && colNumber <= 6 ? 'right' : 'left', vertical: 'middle' },
          border: {
            top: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            left: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            bottom: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            right: { style: 'thin', color: { argb: 'FFE0E0E0' } }
          }
        };

        // Format currency values
        if (colNumber === 6) {
          cell.numFmt = '"R"#,##0.00';
        }
        // Format numbers
        else if (colNumber >= 3 && colNumber <= 5) {
          cell.numFmt = '#,##0';
        }

        // Color code stock levels
        if (colNumber === 7) {
          const stockLevel = cell.value as string;
          if (stockLevel === 'LOW') {
            cell.style.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFF2CC' } };
            cell.style.font = { ...cell.style.font, color: { argb: 'FFD68910' } };
          } else if (stockLevel === 'OUT-OF-STOCK') {
            cell.style.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFDEAEA' } };
            cell.style.font = { ...cell.style.font, color: { argb: 'FFE74C3C' } };
          } else {
            cell.style.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE8F5E8' } };
            cell.style.font = { ...cell.style.font, color: { argb: 'FF27AE60' } };
          }
        }
      });
    });

    // Create Excel Table
    const tableStartRow = 4;
    const tableEndRow = tableStartRow + noteSeriesData.length;
    const tableRange = `A${tableStartRow}:G${tableEndRow}`;

    worksheet.addTable({
      name: 'NoteSeriesDetailsTable',
      ref: tableRange,
      headerRow: true,
      style: {
        theme: 'TableStyleMedium2',
        showRowStripes: true
      },
      columns: headers.map(header => ({ name: header })),
      rows: noteSeriesData
    });
  }

  private async addCoinPortfolioSheet(workbook: ExcelJS.Workbook, auditReport: AuditReportSummary): Promise<void> {
    const worksheet = workbook.addWorksheet('🪙 Coin Portfolio', {
      properties: { tabColor: { argb: 'FFFF8C00' } }
    });

    // Set column widths
    worksheet.columns = [
      { width: 18 }, // A - Denomination
      { width: 15 }, // B - Total Quantity
      { width: 12 }, // C - Total Batches
      { width: 18 }, // D - Total Value
      { width: 25 }, // E - Series Breakdown
      { width: 15 }  // F - Performance
    ];

    // Title row
    const titleRow = worksheet.addRow(['🪙 COIN PORTFOLIO ANALYSIS']);
    worksheet.mergeCells('A1:F1');
    titleRow.getCell(1).style = {
      font: { name: 'Calibri', size: 16, bold: true, color: { argb: 'FFFFFFFF' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFF8C00' } }
    };
    titleRow.height = 30;

    // Date row
    const dateRow = worksheet.addRow([`Generated: ${auditReport.generatedDate.toLocaleString()}`]);
    worksheet.mergeCells('A2:F2');
    dateRow.getCell(1).style = {
      font: { name: 'Calibri', size: 12, italic: true, color: { argb: 'FF666666' } },
      alignment: { horizontal: 'center' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } }
    };

    // Empty row
    worksheet.addRow([]);

    // Prepare coin data
    const coinData: any[][] = [];
    const totalCoinValue = auditReport.coinSummary.reduce((sum, coin) => sum + coin.totalValue, 0);

    auditReport.coinSummary.forEach(coin => {
      const seriesBreakdown = coin.seriesBreakdown.map(series =>
        `${series.seriesName}: ${series.quantity}`
      ).join(', ');

      const performance = this.calculatePerformanceIndicator(coin.totalValue, totalCoinValue);

      coinData.push([
        coin.denominationLabel,
        coin.totalQuantity,
        coin.totalBatches,
        coin.totalValue,
        seriesBreakdown,
        performance
      ]);
    });

    // Add headers
    const headers = ['Denomination', 'Total Quantity', 'Total Batches', 'Total Value', 'Series Breakdown', 'Performance'];
    const headerRow = worksheet.addRow(headers);
    headerRow.height = 25;
    headerRow.eachCell((cell) => {
      cell.style = {
        font: { name: 'Calibri', size: 12, bold: true, color: { argb: 'FFFFFFFF' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFF8C00' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: {
          top: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          bottom: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
        }
      };
    });

    // Add data rows
    coinData.forEach(rowData => {
      const row = worksheet.addRow(rowData);
      row.eachCell((cell, colNumber) => {
        cell.style = {
          font: { name: 'Calibri', size: 11 },
          alignment: { horizontal: colNumber >= 2 && colNumber <= 4 ? 'right' : 'left', vertical: 'middle' },
          border: {
            top: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            left: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            bottom: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            right: { style: 'thin', color: { argb: 'FFE0E0E0' } }
          }
        };

        // Format currency values
        if (colNumber === 4) {
          cell.numFmt = '"R"#,##0.00';
        }
        // Format numbers
        else if (colNumber >= 2 && colNumber <= 3) {
          cell.numFmt = '#,##0';
        }
      });
    });

    // Create Excel Table
    const tableStartRow = 4;
    const tableEndRow = tableStartRow + coinData.length;
    const tableRange = `A${tableStartRow}:F${tableEndRow}`;

    worksheet.addTable({
      name: 'CoinPortfolioTable',
      ref: tableRange,
      headerRow: true,
      style: {
        theme: 'TableStyleMedium6',
        showRowStripes: true
      },
      columns: headers.map(header => ({ name: header })),
      rows: coinData
    });
  }

  private async addLowStockAlertsSheet(workbook: ExcelJS.Workbook, auditReport: AuditReportSummary): Promise<void> {
    const worksheet = workbook.addWorksheet('⚠️ Low Stock Alerts', {
      properties: { tabColor: { argb: 'FFDC3545' } }
    });

    // Set column widths
    worksheet.columns = [
      { width: 12 }, // A - Type
      { width: 25 }, // B - Series
      { width: 18 }, // C - Denomination
      { width: 15 }, // D - Current Quantity
      { width: 15 }, // E - Stock Level
      { width: 18 }, // F - Value
      { width: 20 }  // G - Action Required
    ];

    // Title row
    const titleRow = worksheet.addRow(['⚠️ LOW STOCK ALERTS & CRITICAL ITEMS']);
    worksheet.mergeCells('A1:G1');
    titleRow.getCell(1).style = {
      font: { name: 'Calibri', size: 16, bold: true, color: { argb: 'FFFFFFFF' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFDC3545' } }
    };
    titleRow.height = 30;

    // Date row
    const dateRow = worksheet.addRow([`Generated: ${auditReport.generatedDate.toLocaleString()}`]);
    worksheet.mergeCells('A2:G2');
    dateRow.getCell(1).style = {
      font: { name: 'Calibri', size: 12, italic: true, color: { argb: 'FF666666' } },
      alignment: { horizontal: 'center' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } }
    };

    // Empty row
    worksheet.addRow([]);

    // Check if there are low stock items
    if (auditReport.lowStockItems.length === 0) {
      const noAlertsRow = worksheet.addRow(['✅ No low stock alerts - All inventory levels are healthy']);
      worksheet.mergeCells('A4:G4');
      noAlertsRow.getCell(1).style = {
        font: { name: 'Calibri', size: 14, bold: true, color: { argb: 'FF27AE60' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE8F5E8' } }
      };
      noAlertsRow.height = 40;
      return;
    }

    // Prepare low stock data
    const lowStockData: any[][] = [];
    auditReport.lowStockItems.forEach(item => {
      const actionRequired = item.stockLevel === 'out-of-stock' ? 'URGENT RESTOCK' : 'MONITOR & REORDER';
      lowStockData.push([
        item.type.toUpperCase(),
        item.series,
        item.denomination,
        item.currentQuantity,
        item.stockLevel.toUpperCase(),
        item.value,
        actionRequired
      ]);
    });

    // Add headers
    const headers = ['Type', 'Series', 'Denomination', 'Current Quantity', 'Stock Level', 'Value', 'Action Required'];
    const headerRow = worksheet.addRow(headers);
    headerRow.height = 25;
    headerRow.eachCell((cell) => {
      cell.style = {
        font: { name: 'Calibri', size: 12, bold: true, color: { argb: 'FFFFFFFF' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFDC3545' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: {
          top: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          bottom: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
        }
      };
    });

    // Add data rows
    lowStockData.forEach(rowData => {
      const row = worksheet.addRow(rowData);
      row.eachCell((cell, colNumber) => {
        cell.style = {
          font: { name: 'Calibri', size: 11 },
          alignment: { horizontal: colNumber === 4 || colNumber === 6 ? 'right' : 'left', vertical: 'middle' },
          border: {
            top: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            left: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            bottom: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            right: { style: 'thin', color: { argb: 'FFE0E0E0' } }
          }
        };

        // Format currency values
        if (colNumber === 6) {
          cell.numFmt = '"R"#,##0.00';
        }
        // Format numbers
        else if (colNumber === 4) {
          cell.numFmt = '#,##0';
        }

        // Color code based on urgency
        if (colNumber === 5) {
          const stockLevel = cell.value as string;
          if (stockLevel === 'OUT-OF-STOCK') {
            cell.style.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFDEAEA' } };
            cell.style.font = { ...cell.style.font, color: { argb: 'FFE74C3C' }, bold: true };
          } else if (stockLevel === 'LOW') {
            cell.style.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFF2CC' } };
            cell.style.font = { ...cell.style.font, color: { argb: 'FFD68910' }, bold: true };
          }
        }

        if (colNumber === 7) {
          const action = cell.value as string;
          if (action === 'URGENT RESTOCK') {
            cell.style.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFDEAEA' } };
            cell.style.font = { ...cell.style.font, color: { argb: 'FFE74C3C' }, bold: true };
          } else {
            cell.style.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFF2CC' } };
            cell.style.font = { ...cell.style.font, color: { argb: 'FFD68910' }, bold: true };
          }
        }
      });
    });

    // Create Excel Table
    const tableStartRow = 4;
    const tableEndRow = tableStartRow + lowStockData.length;
    const tableRange = `A${tableStartRow}:G${tableEndRow}`;

    worksheet.addTable({
      name: 'LowStockAlertsTable',
      ref: tableRange,
      headerRow: true,
      style: {
        theme: 'TableStyleMedium3',
        showRowStripes: true
      },
      columns: headers.map(header => ({ name: header })),
      rows: lowStockData
    });
  }

  private async addDenominationBreakdownSheet(workbook: ExcelJS.Workbook, auditReport: AuditReportSummary): Promise<void> {
    const worksheet = workbook.addWorksheet('💰 Denomination Analysis', {
      properties: { tabColor: { argb: 'FF6F42C1' } }
    });

    // Set column widths
    worksheet.columns = [
      { width: 18 }, // A - Denomination
      { width: 12 }, // B - Type
      { width: 15 }, // C - Total Quantity
      { width: 18 }, // D - Total Value
      { width: 15 }, // E - Percentage
      { width: 20 }  // F - Market Share
    ];

    // Title row
    const titleRow = worksheet.addRow(['💰 DENOMINATION BREAKDOWN ANALYSIS']);
    worksheet.mergeCells('A1:F1');
    titleRow.getCell(1).style = {
      font: { name: 'Calibri', size: 16, bold: true, color: { argb: 'FFFFFFFF' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF6F42C1' } }
    };
    titleRow.height = 30;

    // Date row
    const dateRow = worksheet.addRow([`Generated: ${auditReport.generatedDate.toLocaleString()}`]);
    worksheet.mergeCells('A2:F2');
    dateRow.getCell(1).style = {
      font: { name: 'Calibri', size: 12, italic: true, color: { argb: 'FF666666' } },
      alignment: { horizontal: 'center' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } }
    };

    // Empty row
    worksheet.addRow([]);

    // Prepare denomination data
    const denominationData: any[][] = [];
    const totalValue = auditReport.denominationBreakdown.reduce((sum, denom) => sum + denom.totalValue, 0);

    auditReport.denominationBreakdown.forEach(denom => {
      const percentage = totalValue > 0 ? (denom.totalValue / totalValue * 100) : 0;
      const marketShare = this.calculateMarketShare(percentage);

      denominationData.push([
        denom.denominationLabel,
        denom.type.toUpperCase(),
        denom.totalQuantity,
        denom.totalValue,
        `${percentage.toFixed(1)}%`,
        marketShare
      ]);
    });

    // Add headers
    const headers = ['Denomination', 'Type', 'Total Quantity', 'Total Value', 'Percentage', 'Market Share'];
    const headerRow = worksheet.addRow(headers);
    headerRow.height = 25;
    headerRow.eachCell((cell) => {
      cell.style = {
        font: { name: 'Calibri', size: 12, bold: true, color: { argb: 'FFFFFFFF' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF6F42C1' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: {
          top: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          bottom: { style: 'thin', color: { argb: 'FFFFFFFF' } },
          right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
        }
      };
    });

    // Add data rows
    denominationData.forEach(rowData => {
      const row = worksheet.addRow(rowData);
      row.eachCell((cell, colNumber) => {
        cell.style = {
          font: { name: 'Calibri', size: 11 },
          alignment: { horizontal: colNumber >= 3 && colNumber <= 5 ? 'right' : 'left', vertical: 'middle' },
          border: {
            top: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            left: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            bottom: { style: 'thin', color: { argb: 'FFE0E0E0' } },
            right: { style: 'thin', color: { argb: 'FFE0E0E0' } }
          }
        };

        // Format currency values
        if (colNumber === 4) {
          cell.numFmt = '"R"#,##0.00';
        }
        // Format numbers
        else if (colNumber === 3) {
          cell.numFmt = '#,##0';
        }

        // Color code type
        if (colNumber === 2) {
          const type = cell.value as string;
          if (type === 'NOTE') {
            cell.style.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE8F5E8' } };
            cell.style.font = { ...cell.style.font, color: { argb: 'FF27AE60' } };
          } else {
            cell.style.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFEF9E7' } };
            cell.style.font = { ...cell.style.font, color: { argb: 'FFFF8C00' } };
          }
        }
      });
    });

    // Create Excel Table
    const tableStartRow = 4;
    const tableEndRow = tableStartRow + denominationData.length;
    const tableRange = `A${tableStartRow}:F${tableEndRow}`;

    worksheet.addTable({
      name: 'DenominationBreakdownTable',
      ref: tableRange,
      headerRow: true,
      style: {
        theme: 'TableStyleMedium4',
        showRowStripes: true
      },
      columns: headers.map(header => ({ name: header })),
      rows: denominationData
    });
  }

  private async addDetailedInventorySheet(workbook: ExcelJS.Workbook, auditReport: AuditReportSummary): Promise<void> {
    const worksheet = workbook.addWorksheet('📋 Complete Inventory Master List', {
      properties: { tabColor: { argb: 'FF0F5132' } }
    });

    // Set column widths for optimal display
    worksheet.columns = [
      { width: 12 }, // A - Type
      { width: 25 }, // B - Series
      { width: 15 }, // C - Denomination
      { width: 15 }, // D - Quantity
      { width: 12 }, // E - Batches
      { width: 12 }, // F - Singles
      { width: 18 }, // G - Value
      { width: 15 }  // H - Status
    ];

    // Title Section
    const titleRow = worksheet.addRow(['📋 COMPLETE INVENTORY MASTER LIST']);
    titleRow.height = 35;
    worksheet.mergeCells('A1:H1');
    titleRow.getCell(1).style = {
      font: { name: 'Calibri', size: 20, bold: true, color: { argb: 'FFFFFFFF' } },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF0F5132' } },
      alignment: { horizontal: 'center', vertical: 'middle' },
      border: this.getAllBorders()
    };

    // Date row
    const dateRow = worksheet.addRow([`Generated: ${auditReport.generatedDate.toLocaleString()}`]);
    worksheet.mergeCells('A2:H2');
    dateRow.getCell(1).style = {
      font: { name: 'Calibri', size: 12, italic: true, color: { argb: 'FF666666' } },
      alignment: { horizontal: 'center' },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FA' } }
    };

    // Empty row
    worksheet.addRow([]);

    // Prepare all inventory data
    const inventoryData: any[][] = [];

    // Add all note series details
    auditReport.noteSeriesSummary.forEach(series => {
      series.denominations.forEach(denom => {
        inventoryData.push([
          '💵 NOTE',
          series.seriesName,
          denom.denominationLabel,
          denom.quantity,
          denom.batches,
          denom.singles,
          denom.value,
          denom.stockLevel.toUpperCase()
        ]);
      });
    });

    // Add all coin details
    auditReport.coinSummary.forEach(coin => {
      coin.seriesBreakdown.forEach(series => {
        const stockLevel = series.quantity === 0 ? 'OUT-OF-STOCK' :
                          series.quantity < 500 ? 'LOW' : 'NORMAL';

        inventoryData.push([
          '🪙 COIN',
          series.seriesName,
          coin.denominationLabel,
          series.quantity,
          series.batches,
          series.quantity % (series.batches > 0 ? Math.floor(series.quantity / series.batches) : 1),
          series.value,
          stockLevel
        ]);
      });
    });

    // Create the Excel Table
    const tableHeaders = ['Type', 'Series', 'Denomination', 'Quantity', 'Batches', 'Singles', 'Value', 'Status'];
    const tableStartRow = 4;
    const tableEndRow = tableStartRow + inventoryData.length;
    const tableRange = `A${tableStartRow}:H${tableEndRow}`;

    worksheet.addTable({
      name: 'InventoryMasterTable',
      ref: tableRange,
      headerRow: true,
      style: {
        theme: 'TableStyleMedium9',
        showRowStripes: true,
        showColumnStripes: false
      },
      columns: tableHeaders.map(header => ({ name: header })),
      rows: inventoryData
    });

    // Apply custom formatting to the table
    const headerRow = worksheet.getRow(tableStartRow);
    headerRow.height = 30;
    headerRow.eachCell((cell) => {
      cell.style = {
        font: { name: 'Calibri', size: 12, bold: true, color: { argb: 'FFFFFFFF' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF0F5132' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
        border: this.getAllBorders()
      };
    });

    // Format data rows
    for (let i = 0; i < inventoryData.length; i++) {
      const rowNumber = tableStartRow + 1 + i;
      const row = worksheet.getRow(rowNumber);
      row.height = 25;

      row.eachCell((cell, colNumber) => {
        const rowData = inventoryData[i];

        cell.style = {
          font: { name: 'Calibri', size: 11 },
          alignment: {
            horizontal: colNumber === 2 ? 'left' : 'center',
            vertical: 'middle'
          },
          border: this.getAllBorders(),
          fill: {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: i % 2 === 0 ? 'FFFFFFFF' : 'FFF8F9FA' }
          }
        };

        // Special formatting for specific columns
        if (colNumber === 4 || colNumber === 5 || colNumber === 6) { // Quantity, Batches, Singles
          cell.style.font!.bold = true;
          cell.numFmt = '#,##0';
        }

        if (colNumber === 7) { // Value column
          cell.style.font!.bold = true;
          cell.numFmt = '"R "#,##0.00';
        }

        if (colNumber === 8) { // Status column
          const status = rowData[7] as string;
          cell.style.font!.bold = true;
          if (status === 'NORMAL') {
            cell.style.font!.color = { argb: 'FF0F5132' };
          } else if (status === 'LOW') {
            cell.style.font!.color = { argb: 'FF664D03' };
          } else if (status === 'OUT-OF-STOCK') {
            cell.style.font!.color = { argb: 'FF842029' };
          }
        }
      });
    }

    // Add autofilter to the table
    worksheet.autoFilter = tableRange;

    // Freeze the header row
    worksheet.views = [
      { state: 'frozen', xSplit: 0, ySplit: tableStartRow }
    ];
  }

  private calculateMarketShare(percentage: number): string {
    if (percentage >= 40) return '🏆 DOMINANT';
    if (percentage >= 25) return '💪 MAJOR';
    if (percentage >= 15) return '📈 SIGNIFICANT';
    if (percentage >= 5) return '📊 MODERATE';
    return '🔍 MINOR';
  }
}
