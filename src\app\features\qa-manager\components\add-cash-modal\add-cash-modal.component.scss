// Remove this duplicate - will be handled in the global styles section

// Make the Material dialog container transparent
:host ::ng-deep .add-cash-modal-panel .mat-mdc-dialog-container {
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
}

:host ::ng-deep .modern-modal-panel .mat-mdc-dialog-container {
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
}

// Modern Container with Better Colors and Transparency
.add-cash-modal-container {
  width: 100%; // Use full available width from service configuration
  max-width: 100%; // Remove max-width constraint
  max-height: 90vh;
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.85) 0%,
    rgba(241, 245, 249, 0.90) 50%,
    rgba(236, 242, 248, 0.92) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 32px 64px rgba(15, 23, 42, 0.15),
    0 16px 32px rgba(15, 23, 42, 0.10),
    0 8px 16px rgba(15, 23, 42, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(226, 232, 240, 0.8);
  overflow: hidden;
  position: relative;
  animation: modalSlideIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

// Gradient Header
.modal-header {
  background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    z-index: 1;
    position: relative;

    .header-icon {
      width: 64px;
      height: 64px;
      background: rgba(255,255,255,0.2);
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255,255,255,0.3);

      mat-icon {
        font-size: 2rem;
        color: white;
      }
    }

    .header-text {
      h2 {
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
      }

      p {
        color: rgba(255,255,255,0.9);
        margin: 0.5rem 0 0 0;
        font-size: 1rem;
        font-weight: 400;
      }
    }
  }

  .close-button {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
    z-index: 1;
    position: relative;

    &:hover {
      background: rgba(255,255,255,0.3);
      transform: scale(1.05);
    }
  }
}

// Main Content Area
.modal-content {
  padding: 2rem;
  max-height: 60vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.6) transparent;
  background: rgba(255, 255, 255, 0.4);

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.6);
    border-radius: 3px;

    &:hover {
      background: rgba(148, 163, 184, 0.8);
    }
  }
}

// Form Sections
.form-section {
  margin-bottom: 2.5rem;
  animation: slideInUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:last-child {
    margin-bottom: 0;
  }

  .section-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    position: relative;

    .step-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
      color: white;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.25rem;
      box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    }

    h3 {
      color: var(--absa-dark-blue, #1e293b);
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0;
      flex: 1;
    }

    .pre-selected-badge {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 500;
      box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
      height: 40px; // Match the step-icon height for perfect alignment
      min-width: fit-content;

      mat-icon {
        font-size: 1rem;
        line-height: 1;
      }
    }
  }
}

// Series Selection Grid
.series-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;

  .series-card {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 16px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    backdrop-filter: blur(10px);

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 32px rgba(15, 23, 42, 0.15);
      border-color: rgba(220, 38, 38, 0.5);
    }

    &.selected {
      border-color: var(--absa-red);
      background: linear-gradient(135deg, rgba(220, 38, 38, 0.1) 0%, rgba(185, 28, 60, 0.1) 100%);
      box-shadow: 0 8px 24px rgba(220, 38, 38, 0.2);

      .series-icon {
        background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
        color: white;

        mat-icon {
          color: white !important;
        }
      }
    }

    .series-icon {
      width: 48px;
      height: 48px;
      background: rgba(148, 163, 184, 0.2);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1rem;
      transition: all 0.3s ease;

      mat-icon {
        font-size: 1.5rem;
        color: var(--absa-gray-dark, #374151);
      }
    }

    .series-info {
      h4 {
        color: var(--absa-dark-blue, #1e293b);
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0 0 0.5rem 0;
      }

      p {
        color: var(--absa-gray-medium, #64748b);
        font-size: 0.875rem;
        margin: 0;
        line-height: 1.4;
      }
    }

    .selection-indicator {
      position: absolute;
      top: 1rem;
      right: 1rem;
      color: var(--absa-red);
      animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      mat-icon {
        font-size: 1.25rem;
      }
    }
  }
}

// Denomination Selection Grid
.denomination-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;

  .denomination-card {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 16px;
    padding: 1.25rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    backdrop-filter: blur(10px);
    text-align: center;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(15, 23, 42, 0.12);
      border-color: rgba(220, 38, 38, 0.5);
    }

    &.selected {
      border-color: var(--absa-red);
      background: linear-gradient(135deg, rgba(220, 38, 38, 0.1) 0%, rgba(185, 28, 60, 0.1) 100%);
      box-shadow: 0 6px 20px rgba(220, 38, 38, 0.2);

      .denomination-icon {
        background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
        color: white;

        mat-icon {
          color: white !important;
        }
      }
    }

    .denomination-content {
      .denomination-icon {
        width: 40px;
        height: 40px;
        background: rgba(148, 163, 184, 0.2);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem auto;
        transition: all 0.3s ease;

        mat-icon {
          font-size: 1.25rem;
          color: var(--absa-gray-dark, #374151);
        }
      }

      .denomination-info {
        h4 {
          color: var(--absa-dark-blue, #1e293b);
          font-size: 1.1rem;
          font-weight: 600;
          margin: 0 0 0.25rem 0;
        }

        p {
          color: var(--absa-gray-medium, #64748b);
          font-size: 0.8rem;
          margin: 0;
        }
      }
    }

    .selection-indicator {
      position: absolute;
      top: 0.75rem;
      right: 0.75rem;
      color: var(--absa-red);
      animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      mat-icon {
        font-size: 1rem;
      }
    }
  }
}

// Quantity Controls
.quantity-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;

  .quantity-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(226, 232, 240, 0.6);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--absa-light-blue);
      box-shadow: 0 4px 12px rgba(0,102,204,0.1);
      background: rgba(255, 255, 255, 0.9);
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 1rem;

      mat-icon {
        color: var(--absa-dark-blue);
        font-size: 1.25rem;
      }

      h4 {
        color: var(--absa-dark-blue);
        font-size: 1rem;
        font-weight: 600;
        margin: 0;
        flex: 1;
      }

      .helper-text {
        color: var(--absa-gray-medium);
        font-size: 0.8rem;
        font-weight: 400;
      }
    }

    .input-container {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.75rem;

      .quantity-btn {
        width: 36px;
        height: 36px;
        min-width: 36px;
        background: rgba(148, 163, 184, 0.1);
        border: 1px solid rgba(148, 163, 184, 0.3);
        color: var(--absa-dark-blue);

        &:hover:not(:disabled) {
          background: var(--absa-light-blue);
          color: white;
          border-color: var(--absa-light-blue);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        mat-icon {
          font-size: 1rem;
        }
      }

      .quantity-input {
        flex: 1;
        text-align: center;
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--absa-dark-blue);
        border: 2px solid rgba(226, 232, 240, 0.8);
        border-radius: 8px;
        padding: 0.75rem;
        background: rgba(255, 255, 255, 0.9);
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: var(--absa-light-blue);
          box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
        }
      }
    }

    .quantity-display {
      text-align: center;

      .notes-count {
        color: var(--absa-gray-dark);
        font-size: 0.9rem;
        font-weight: 500;
      }
    }
  }
}

// Reason Field
.reason-field {
  width: 100%;

  mat-label {
    color: var(--absa-dark-blue);
    font-weight: 500;
  }

  textarea {
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
  }

  .mat-form-field-outline {
    color: #E5E7EB;
  }

  &.mat-focused .mat-form-field-outline-thick {
    color: var(--absa-red);
  }
}

// Modern Summary Display
.summary-display {
  margin-top: 1.5rem;

  .summary-preview {
    .preview-card {
      background: linear-gradient(135deg,
        rgba(255,255,255,0.9) 0%,
        rgba(248,250,252,0.95) 100%);
      border: 1px solid rgba(148, 163, 184, 0.2);
      border-radius: 20px;
      padding: 1.5rem;
      box-shadow:
        0 8px 32px rgba(15, 23, 42, 0.08),
        0 4px 16px rgba(15, 23, 42, 0.04);
      backdrop-filter: blur(15px);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg,
          var(--absa-red) 0%,
          var(--absa-light-blue) 50%,
          var(--absa-gold) 100%);
      }

      .preview-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;

        .preview-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, var(--absa-light-blue), var(--absa-dark-blue));
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 12px rgba(0,102,204,0.2);

          mat-icon {
            color: white;
            font-size: 1.5rem;
          }
        }

        .preview-title {
          h4 {
            margin: 0 0 0.25rem 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--absa-dark-blue);
          }

          p {
            margin: 0;
            font-size: 0.9rem;
            color: var(--absa-gray-medium);
            font-weight: 500;
          }
        }
      }

      .preview-metrics {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        margin-bottom: 1.5rem;

        .metric-item {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          flex: 1;

          .metric-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--absa-gray-light), #f8fafc);
            border: 1px solid rgba(0,102,204,0.1);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;

            mat-icon {
              font-size: 1.25rem;
              color: var(--absa-light-blue);
            }
          }

          .metric-details {
            display: flex;
            flex-direction: column;

            .metric-value {
              font-size: 1.25rem;
              font-weight: 700;
              color: var(--absa-dark-blue);
              line-height: 1.2;
            }

            .metric-label {
              font-size: 0.8rem;
              color: var(--absa-gray-medium);
              font-weight: 500;
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }
          }

          &.total-value {
            .metric-icon {
              background: linear-gradient(135deg, var(--absa-gold), #FFB81C);

              mat-icon {
                color: white;
              }
            }

            .metric-value {
              color: var(--absa-red);
              font-size: 1.4rem;
            }
          }
        }

        .metric-divider {
          width: 1px;
          height: 40px;
          background: linear-gradient(to bottom,
            transparent,
            rgba(0,102,204,0.2),
            transparent);
        }
      }
      .breakdown-section {
        border-top: 1px solid rgba(0,102,204,0.1);
        padding-top: 1rem;

        .breakdown-title {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          margin-bottom: 0.75rem;

          mat-icon {
            font-size: 1rem;
            color: var(--absa-light-blue);
          }

          span {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--absa-dark-blue);
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }

        .breakdown-items {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;

          .breakdown-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0.75rem;
            background: rgba(0,102,204,0.03);
            border-radius: 8px;
            border-left: 3px solid var(--absa-light-blue);

            .breakdown-label {
              font-size: 0.85rem;
              color: var(--absa-gray-medium);
              font-weight: 500;
            }

            .breakdown-value {
              font-size: 0.85rem;
              font-weight: 600;
              color: var(--absa-dark-blue);
            }
          }
        }
      }
    }
  }
}

// Modal Actions
.modal-actions {
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.9) 0%,
    rgba(255, 255, 255, 0.8) 100%);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(226, 232, 240, 0.6);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;

  .cancel-btn {
    border: 2px solid var(--absa-gray-medium);
    color: var(--absa-gray-dark);
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--absa-red);
      color: var(--absa-red);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(227, 24, 55, 0.2);
    }

    mat-icon {
      margin-right: 0.5rem;
    }
  }

  .add-btn {
    background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(227, 24, 55, 0.3);

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(227, 24, 55, 0.4);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    mat-icon {
      margin-right: 0.5rem;
    }
  }
}

// Animations
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes checkmark {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// Responsive Design - Much smaller content for mobile
@media (max-width: 768px) {
  .add-cash-modal-container {
    width: 100%; // Use full available width
    max-height: 90vh;
    transform: scale(0.9);
  }

  .modal-header {
    padding: 0.75rem;

    .header-content {
      gap: 0.5rem;

      .header-icon {
        width: 28px;
        height: 28px;

        mat-icon {
          font-size: 0.875rem;
        }
      }

      .header-text h2 {
        font-size: 1rem;
      }

      .header-text p {
        font-size: 0.625rem;
      }
    }
  }

  .modal-content {
    padding: 0.75rem;
  }

  .series-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
  }

  .series-card {
    padding: 0.5rem;
    border-radius: 6px;

    .series-icon {
      width: 24px;
      height: 24px;
      margin-bottom: 0.25rem;

      mat-icon {
        font-size: 0.875rem;
      }
    }

    .series-info h4 {
      font-size: 0.625rem;
      margin-bottom: 0.125rem;
    }

    .series-info p {
      font-size: 0.5rem;
    }
  }

  .denomination-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.375rem;
  }

  .denomination-card {
    padding: 0.375rem;
    border-radius: 4px;

    .denomination-icon {
      width: 20px;
      height: 20px;
      margin-bottom: 0.25rem;

      mat-icon {
        font-size: 0.75rem;
      }
    }

    .denomination-info h4 {
      font-size: 0.5rem;
      margin-bottom: 0.125rem;
    }

    .denomination-info p {
      font-size: 0.375rem;
    }
  }

  .quantity-controls {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .quantity-input-group {
    .quantity-label {
      font-size: 0.625rem;
      margin-bottom: 0.25rem;
    }

    .quantity-input {
      padding: 0.375rem;
      font-size: 0.75rem;
      border-radius: 4px;
    }
  }

  .modal-actions {
    padding: 0.5rem 0.75rem;
    flex-direction: column;
    gap: 0.375rem;

    .cancel-btn,
    .add-btn {
      width: 100%;
      justify-content: center;
      padding: 0.5rem;
      font-size: 0.625rem;
      border-radius: 4px;
    }
  }
}

@media (max-width: 480px) {
  .add-cash-modal-container {
    width: 100%; // Use full available width
    max-height: 100vh;
    border-radius: 0;
    transform: scale(0.85);
  }

  .modal-header {
    padding: 0.5rem;

    .header-content {
      gap: 0.25rem;

      .header-icon {
        width: 24px;
        height: 24px;

        mat-icon {
          font-size: 0.75rem;
        }
      }

      .header-text h2 {
        font-size: 0.875rem;
      }

      .header-text p {
        font-size: 0.5rem;
      }
    }
  }

  .modal-content {
    padding: 0.5rem;
  }

  .series-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.25rem;
  }

  .series-card {
    padding: 0.25rem;

    .series-icon {
      width: 20px;
      height: 20px;
      margin-bottom: 0.125rem;

      mat-icon {
        font-size: 0.625rem;
      }
    }

    .series-info h4 {
      font-size: 0.5rem;
      margin-bottom: 0.0625rem;
    }

    .series-info p {
      font-size: 0.375rem;
    }
  }

  .denomination-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.25rem;
  }

  .denomination-card {
    padding: 0.25rem;

    .denomination-icon {
      width: 16px;
      height: 16px;
      margin-bottom: 0.125rem;

      mat-icon {
        font-size: 0.625rem;
      }
    }

    .denomination-info h4 {
      font-size: 0.375rem;
      margin-bottom: 0.0625rem;
    }

    .denomination-info p {
      font-size: 0.25rem;
    }
  }

  .quantity-controls {
    gap: 0.25rem;
  }

  .quantity-input-group {
    .quantity-label {
      font-size: 0.5rem;
      margin-bottom: 0.125rem;
    }

    .quantity-input {
      padding: 0.25rem;
      font-size: 0.625rem;
      border-radius: 3px;
    }
  }

  .modal-actions {
    padding: 0.375rem 0.5rem;
    gap: 0.25rem;

    .cancel-btn,
    .add-btn {
      padding: 0.375rem;
      font-size: 0.5rem;
      border-radius: 3px;
    }
  }
}

@media (max-width: 320px) {
  .add-cash-modal-container {
    transform: scale(0.8);
  }

  .modal-header {
    padding: 0.375rem;

    .header-content {
      gap: 0.125rem;

      .header-icon {
        width: 20px;
        height: 20px;

        mat-icon {
          font-size: 0.625rem;
        }
      }

      .header-text h2 {
        font-size: 0.75rem;
      }

      .header-text p {
        font-size: 0.375rem;
      }
    }
  }

  .modal-content {
    padding: 0.375rem;
  }

  .series-grid {
    grid-template-columns: 1fr;
    gap: 0.125rem;
  }

  .series-card {
    padding: 0.125rem;

    .series-icon {
      width: 16px;
      height: 16px;
      margin-bottom: 0.0625rem;

      mat-icon {
        font-size: 0.5rem;
      }
    }

    .series-info h4 {
      font-size: 0.375rem;
      margin-bottom: 0.03125rem;
    }

    .series-info p {
      font-size: 0.25rem;
    }
  }

  .denomination-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.125rem;
  }

  .denomination-card {
    padding: 0.125rem;

    .denomination-icon {
      width: 12px;
      height: 12px;
      margin-bottom: 0.0625rem;

      mat-icon {
        font-size: 0.5rem;
      }
    }

    .denomination-info h4 {
      font-size: 0.25rem;
      margin-bottom: 0.03125rem;
    }

    .denomination-info p {
      font-size: 0.1875rem;
    }
  }

  .quantity-input-group {
    .quantity-label {
      font-size: 0.375rem;
      margin-bottom: 0.0625rem;
    }

    .quantity-input {
      padding: 0.125rem;
      font-size: 0.5rem;
      border-radius: 2px;
    }
  }

  .modal-actions {
    padding: 0.25rem 0.375rem;
    gap: 0.125rem;

    .cancel-btn,
    .add-btn {
      padding: 0.25rem;
      font-size: 0.375rem;
      border-radius: 2px;
    }
  }
}

// Global Modal Styles - Subtle backdrop blur that preserves background visibility
:host ::ng-deep {
  // Target all possible backdrop classes with maximum specificity
  .add-cash-modal-backdrop,
  .cdk-overlay-backdrop.add-cash-modal-backdrop,
  .mat-dialog-backdrop.add-cash-modal-backdrop {
    backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    background: rgba(15, 23, 42, 0.4) !important;
    animation: backdropFadeIn 0.3s ease-out !important;
    transition: backdrop-filter 0.3s ease, background 0.3s ease !important;
    cursor: pointer !important;

    // Add subtle hover effect to indicate interactivity
    &:hover {
      backdrop-filter: blur(12px) saturate(1.2) brightness(0.85) !important;
      -webkit-backdrop-filter: blur(12px) saturate(1.2) brightness(0.85) !important;
      background: rgba(15, 23, 42, 0.5) !important;
    }
  }

  .add-cash-modal-panel {
    border-radius: 24px !important;
    overflow: hidden !important;
    box-shadow: 0 32px 64px rgba(0,0,0,0.12) !important;

    .mat-mdc-dialog-container {
      border-radius: 24px !important;
      overflow: hidden !important;
      padding: 0 !important;
    }
  }

  .modern-modal-panel {
    .mat-mdc-dialog-container {
      max-width: none !important;
      max-height: none !important;
    }
  }
}

// Additional global styles to ensure backdrop blur works
::ng-deep {
  // Target CDK overlay backdrop
  .cdk-overlay-backdrop.add-cash-modal-backdrop {
    backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    background: rgba(15, 23, 42, 0.4) !important;
  }

  // Target Material dialog backdrop
  .mat-dialog-backdrop.add-cash-modal-backdrop {
    backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    background: rgba(15, 23, 42, 0.4) !important;
  }

  // Fallback for any backdrop with our class
  .add-cash-modal-backdrop {
    backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    background: rgba(15, 23, 42, 0.4) !important;
  }
}

@keyframes backdropFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px) !important;
    -webkit-backdrop-filter: blur(0px) !important;
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
